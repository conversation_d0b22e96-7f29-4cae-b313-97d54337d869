# Generated by Django 5.2.1 on 2025-05-29 12:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0006_remove_featuredprojectssection_subtitle_ar_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AchievementsItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('icon_name_en', models.CharField(max_length=100, verbose_name='Icon Name (English)')),
                ('icon_name_ar', models.CharField(max_length=100, verbose_name='Icon Name (Arabic)')),
                ('achievement_value_en', models.CharField(max_length=100, verbose_name='Achievement Value (English)')),
                ('achievement_value_ar', models.Char<PERSON>ield(max_length=100, verbose_name='Achievement Value (Arabic)')),
                ('title_en', models.CharField(max_length=100, verbose_name='Title (English)')),
                ('title_ar', models.CharField(max_length=100, verbose_name='Title (Arabic)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='achievements_items_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Achievements Item',
                'verbose_name_plural': 'Achievements Items',
                'db_table': 'homepage_achievements_items',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='AchievementsItemRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('changes', models.JSONField(verbose_name='Changes Made')),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('achievements_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions', to='homepage.achievementsitem')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Achievements Item Revision',
                'verbose_name_plural': 'Achievements Item Revisions',
                'db_table': 'homepage_achievements_item_revisions',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='AchievementsSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('badge_en', models.CharField(max_length=100, verbose_name='Badge (English)')),
                ('badge_ar', models.CharField(max_length=100, verbose_name='Badge (Arabic)')),
                ('title_en', models.CharField(max_length=100, verbose_name='Title (English)')),
                ('title_ar', models.CharField(max_length=100, verbose_name='Title (Arabic)')),
                ('description_en', models.TextField(max_length=500, verbose_name='Description (English)')),
                ('description_ar', models.TextField(max_length=500, verbose_name='Description (Arabic)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='achievements_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Achievements Section',
                'verbose_name_plural': 'Achievements Sections',
                'db_table': 'homepage_achievements_section',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='AchievementsSectionRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('changes', models.JSONField(verbose_name='Changes Made')),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('achievements_section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions', to='homepage.achievementssection')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Achievements Section Revision',
                'verbose_name_plural': 'Achievements Section Revisions',
                'db_table': 'homepage_achievements_revisions',
                'ordering': ['-updated_at'],
            },
        ),
    ]

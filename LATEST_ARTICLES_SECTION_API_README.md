# Latest Articles Section API Documentation

## Overview

The Latest Articles Section API provides comprehensive endpoints for managing the latest articles section content on the homepage. It supports bilingual content (English/Arabic), revision tracking, and both admin and public access patterns.

## Data Structure

### Latest Articles Section
- **badge**: Bilingual badge text (English/Arabic)
- **title**: Bilingual main title (English/Arabic)  
- **description**: Bilingual description text (English/Arabic)

## Base URLs

- **Admin Endpoints**: `/api/admin/home-page/latest-articles/`
- **Public Endpoints**: `/api/home-page/latest-articles/`

## Authentication

- **Admin Endpoints**: Require authentication (Bearer token)
- **Public Endpoints**: No authentication required

---

## Admin Endpoints (Authentication Required)

### 1. Latest Articles Section Management

#### Get Latest Articles Section
```http
GET /api/admin/home-page/latest-articles/
Authorization: Bearer <your-token>
```

**Response:**
```json
{
  "success": true,
  "message": "Latest articles content retrieved successfully",
  "data": {
    "id": 1,
    "badge": {
      "en": "Latest Articles",
      "ar": "أحدث المقالات"
    },
    "title": {
      "en": "Stay Updated with Our Latest Insights",
      "ar": "ابق على اطلاع بأحدث رؤانا"
    },
    "description": {
      "en": "Discover our latest articles, insights, and industry updates.",
      "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User"
    }
  }
}
```

#### Update Latest Articles Section
```http
PUT /api/admin/home-page/latest-articles/
Authorization: Bearer <your-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "badge": {
    "en": "Latest Articles",
    "ar": "أحدث المقالات"
  },
  "title": {
    "en": "Stay Updated with Our Latest Insights", 
    "ar": "ابق على اطلاع بأحدث رؤانا"
  },
  "description": {
    "en": "Discover our latest articles, insights, and industry updates.",
    "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Latest articles section content updated successfully",
  "data": {
    "id": 1,
    "badge": {
      "en": "Latest Articles",
      "ar": "أحدث المقالات"
    },
    "title": {
      "en": "Stay Updated with Our Latest Insights",
      "ar": "ابق على اطلاع بأحدث رؤانا"
    },
    "description": {
      "en": "Discover our latest articles, insights, and industry updates.",
      "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T15:00:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User"
    }
  }
}
```

#### Update Specific Section (PATCH)
```http
PATCH /api/admin/home-page/latest-articles/section/
Authorization: Bearer <your-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "section": "title",
  "data": {
    "en": "Updated Title in English",
    "ar": "العنوان المحدث بالعربية"
  }
}
```

**Available sections:** `badge`, `title`, `description`

#### Get Latest Articles Section History
```http
GET /api/admin/home-page/latest-articles/history/?page=1&per_page=10
Authorization: Bearer <your-token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 10, max: 50)

**Response:**
```json
{
  "success": true,
  "data": {
    "total": 5,
    "page": 1,
    "per_page": 10,
    "revisions": [
      {
        "id": 3,
        "changes": {
          "title": {
            "old": {
              "en": "Old Title",
              "ar": "العنوان القديم"
            },
            "new": {
              "en": "Stay Updated with Our Latest Insights",
              "ar": "ابق على اطلاع بأحدث رؤانا"
            }
          }
        },
        "updated_by": {
          "id": 1,
          "email": "<EMAIL>",
          "first_name": "Admin",
          "last_name": "User"
        },
        "updated_at": "2024-01-20T15:00:00Z"
      }
    ]
  }
}
```

---

## Public Endpoints (No Authentication Required)

### Get Latest Articles Section (English)
```http
GET /api/home-page/latest-articles/en/
```

**Response:**
```json
{
  "success": true,
  "message": "Latest articles content retrieved successfully",
  "data": {
    "id": 1,
    "badge": "Latest Articles",
    "title": "Stay Updated with Our Latest Insights",
    "description": "Discover our latest articles, insights, and industry updates.",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z"
  }
}
```

### Get Latest Articles Section (Arabic)
```http
GET /api/home-page/latest-articles/ar/
```

**Response:**
```json
{
  "success": true,
  "message": "Latest articles content retrieved successfully",
  "data": {
    "id": 1,
    "badge": "أحدث المقالات",
    "title": "ابق على اطلاع بأحدث رؤانا",
    "description": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة.",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z"
  }
}
```

---

## CURL Test Examples

### 1. Get Latest Articles Section (Admin)
```bash
curl -X GET "http://localhost:8000/api/admin/home-page/latest-articles/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json"
```

### 2. Update Latest Articles Section
```bash
curl -X PUT "http://localhost:8000/api/admin/home-page/latest-articles/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "badge": {
      "en": "Latest Articles",
      "ar": "أحدث المقالات"
    },
    "title": {
      "en": "Stay Updated with Our Latest Insights",
      "ar": "ابق على اطلاع بأحدث رؤانا"
    },
    "description": {
      "en": "Discover our latest articles, insights, and industry updates.",
      "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
    }
  }'
```

### 3. Update Specific Section (PATCH)
```bash
curl -X PATCH "http://localhost:8000/api/admin/home-page/latest-articles/section/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "section": "title",
    "data": {
      "en": "Updated Title in English",
      "ar": "العنوان المحدث بالعربية"
    }
  }'
```

### 4. Get Latest Articles History
```bash
curl -X GET "http://localhost:8000/api/admin/home-page/latest-articles/history/?page=1&per_page=10" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json"
```

### 5. Get Public Latest Articles Section (English)
```bash
curl -X GET "http://localhost:8000/api/home-page/latest-articles/en/"
```

### 6. Get Public Latest Articles Section (Arabic)
```bash
curl -X GET "http://localhost:8000/api/home-page/latest-articles/ar/"
```

---

## Error Responses

### 400 Bad Request - Validation Error
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "title": {
      "en": ["This field is required"]
    }
  },
  "error_code": "VALIDATION_ERROR"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication credentials were not provided.",
  "error_code": "AUTHENTICATION_REQUIRED"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Permission denied. You don't have access to manage home page content.",
  "error_code": "PERMISSION_DENIED"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Latest articles content not found",
  "error_code": "LATEST_ARTICLES_NOT_FOUND"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error occurred",
  "error_code": "INTERNAL_ERROR"
}
```

---

## Expected Test Results

### Update Latest Articles Section - Expected Response
```json
{
  "success": true,
  "message": "Latest articles section content updated successfully",
  "data": {
    "id": 1,
    "badge": {
      "en": "Latest Articles",
      "ar": "أحدث المقالات"
    },
    "title": {
      "en": "Stay Updated with Our Latest Insights",
      "ar": "ابق على اطلاع بأحدث رؤانا"
    },
    "description": {
      "en": "Discover our latest articles, insights, and industry updates.",
      "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T16:30:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User"
    }
  }
}
```

### Update Specific Section (PATCH) - Expected Response
```json
{
  "success": true,
  "message": "Latest articles section updated successfully",
  "data": {
    "id": 1,
    "badge": {
      "en": "Latest Articles",
      "ar": "أحدث المقالات"
    },
    "title": {
      "en": "Updated Title in English",
      "ar": "العنوان المحدث بالعربية"
    },
    "description": {
      "en": "Discover our latest articles, insights, and industry updates.",
      "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T16:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User"
    }
  }
}
```

### Get Latest Articles History - Expected Response
```json
{
  "success": true,
  "data": {
    "total": 8,
    "page": 1,
    "per_page": 10,
    "revisions": [
      {
        "id": 5,
        "changes": {
          "description": {
            "old": {
              "en": "Old description text",
              "ar": "نص الوصف القديم"
            },
            "new": {
              "en": "Discover our latest articles, insights, and industry updates.",
              "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
            }
          }
        },
        "updated_by": {
          "id": 1,
          "email": "<EMAIL>",
          "first_name": "Admin",
          "last_name": "User"
        },
        "updated_at": "2024-01-20T16:30:00Z"
      },
      {
        "id": 4,
        "changes": {
          "title": {
            "old": {
              "en": "Previous Title",
              "ar": "العنوان السابق"
            },
            "new": {
              "en": "Stay Updated with Our Latest Insights",
              "ar": "ابق على اطلاع بأحدث رؤانا"
            }
          }
        },
        "updated_by": {
          "id": 2,
          "email": "<EMAIL>",
          "first_name": "Editor",
          "last_name": "User"
        },
        "updated_at": "2024-01-20T15:15:00Z"
      }
    ]
  }
}
```

---

## Features

- ✅ **Full CRUD operations** for latest articles section
- ✅ **Bilingual support** (English/Arabic)
- ✅ **Revision tracking** with change history
- ✅ **Permission-based access control**
- ✅ **Pagination** for revision history
- ✅ **Comprehensive validation**
- ✅ **Section-specific updates** (PATCH support)
- ✅ **Public API endpoints** for frontend consumption
- ✅ **Detailed error handling** with specific error codes

## Content Guidelines

### Badge Text
- Keep it short and descriptive (e.g., "Latest Articles", "Recent Posts")
- Should indicate the nature of the content section
- Maximum 100 characters per language

### Title
- Should be engaging and descriptive
- Clearly indicates what users will find in this section
- Maximum 100 characters per language
- Examples:
  - "Stay Updated with Our Latest Insights"
  - "Discover Our Recent Articles"
  - "Latest News and Updates"

### Description
- Provides more context about the articles section
- Should encourage users to explore the content
- Maximum 500 characters per language
- Examples:
  - "Discover our latest articles, insights, and industry updates."
  - "Stay informed with our expert analysis and thought leadership."
  - "Explore our collection of recent articles and industry insights."

## API Endpoint Summary

### Admin Endpoints (Authentication Required)
```
GET    /api/admin/home-page/latest-articles/           # Get section content
PUT    /api/admin/home-page/latest-articles/           # Update section content
PATCH  /api/admin/home-page/latest-articles/           # Update section content (partial)
PATCH  /api/admin/home-page/latest-articles/section/   # Update specific section
GET    /api/admin/home-page/latest-articles/history/   # Get revision history
```

### Public Endpoints (No Authentication)
```
GET    /api/home-page/latest-articles/en/              # Get English content
GET    /api/home-page/latest-articles/ar/              # Get Arabic content
```

## Validation Rules

### Badge Validation
- **Required**: Both English and Arabic versions
- **Max Length**: 100 characters per language
- **Format**: Plain text, no HTML allowed

### Title Validation
- **Required**: Both English and Arabic versions
- **Max Length**: 100 characters per language
- **Format**: Plain text, no HTML allowed

### Description Validation
- **Required**: Both English and Arabic versions
- **Max Length**: 500 characters per language
- **Format**: Plain text, no HTML allowed

## Additional Test Examples

### Test Invalid Data (Missing Required Field)
```bash
curl -X PUT "http://localhost:8000/api/admin/home-page/latest-articles/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "badge": {
      "en": "Latest Articles"
    },
    "title": {
      "en": "Stay Updated with Our Latest Insights",
      "ar": "ابق على اطلاع بأحدث رؤانا"
    },
    "description": {
      "en": "Discover our latest articles, insights, and industry updates.",
      "ar": "اكتشف أحدث مقالاتنا ورؤانا وتحديثات الصناعة."
    }
  }'
```

**Expected Error Response:**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "badge": {
      "ar": ["This field is required"]
    }
  },
  "error_code": "VALIDATION_ERROR"
}
```

### Test Unauthorized Access
```bash
curl -X GET "http://localhost:8000/api/admin/home-page/latest-articles/"
```

**Expected Error Response:**
```json
{
  "success": false,
  "message": "Authentication credentials were not provided.",
  "error_code": "AUTHENTICATION_REQUIRED"
}
```

### Test Invalid Section Update
```bash
curl -X PATCH "http://localhost:8000/api/admin/home-page/latest-articles/section/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "section": "invalid_section",
    "data": {
      "en": "Some text",
      "ar": "نص ما"
    }
  }'
```

**Expected Error Response:**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "section": ["Invalid section. Must be one of: badge, title, description"]
  },
  "error_code": "VALIDATION_ERROR"
}
```

## Implementation Status

- ✅ **Models**: Complete with bilingual support and revision tracking
- ✅ **Serializers**: Full validation and nested structure support
- ✅ **Views**: Complete CRUD operations with error handling
- ✅ **URLs**: All endpoints properly configured
- ✅ **Admin Interface**: Full admin panel integration
- ✅ **Public APIs**: Optimized endpoints for frontend consumption
- ✅ **Documentation**: Comprehensive API documentation

## Notes

- All admin endpoints require proper authentication and permissions
- Public endpoints are optimized for frontend consumption
- Revision history tracks all changes for audit purposes
- All timestamps are in ISO 8601 format (UTC)
- Content validation ensures data integrity
- Supports both full updates (PUT) and partial updates (PATCH)
- Section-specific updates allow granular content management
- Admin interface available at `/admin/` for easy content management
- All changes are tracked with user attribution and timestamps

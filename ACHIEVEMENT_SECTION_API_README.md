# Achievement Section API Documentation

## Overview

The Achievement Section API provides comprehensive endpoints for managing achievement content on the homepage. It supports bilingual content (English/Arabic), revision tracking, and both admin and public access patterns.

## Data Structure

### Achievement Section
- **badge**: Bilingual badge text (English/Arabic)
- **title**: Bilingual main title (English/Arabic)  
- **supporting_text**: Bilingual supporting/description text (English/Arabic)

### Achievement Items
- **icon_name**: Icon identifier (not translatable - e.g., "trophy", "users", "star")
- **achievement_value**: Numeric value with symbols (not translatable - e.g., "100+", "5 Years", "99%")
- **title**: Bilingual item title (English/Arabic only)

## Base URLs

- **Admin Endpoints**: `/api/admin/home-page/achievements/`
- **Public Endpoints**: `/api/home-page/achievements/`

## Authentication

- **Admin Endpoints**: Require authentication (Bearer token)
- **Public Endpoints**: No authentication required

---

## Admin Endpoints (Authentication Required)

### 1. Achievement Section Management

#### Get Achievement Section
```http
GET /api/admin/home-page/achievements/
Authorization: Bearer <your-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "badge": {
      "en": "Our Achievements",
      "ar": "إنجازاتنا"
    },
    "title": {
      "en": "What We Have Accomplished",
      "ar": "ما حققناه"
    },
    "supporting_text": {
      "en": "Over the years, we have achieved remarkable milestones.",
      "ar": "على مر السنين، حققنا إنجازات رائعة."
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User"
    }
  }
}
```

#### Update Achievement Section
```http
PUT /api/admin/home-page/achievements/
Authorization: Bearer <your-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "badge": {
    "en": "Our Achievements",
    "ar": "إنجازاتنا"
  },
  "title": {
    "en": "What We Have Accomplished", 
    "ar": "ما حققناه"
  },
  "supporting_text": {
    "en": "Over the years, we have achieved remarkable milestones.",
    "ar": "على مر السنين، حققنا إنجازات رائعة."
  }
}
```

#### Get Achievement Section History
```http
GET /api/admin/home-page/achievements/history/?page=1&per_page=10
Authorization: Bearer <your-token>
```

### 2. Achievement Items Management

#### List Achievement Items
```http
GET /api/admin/home-page/achievements/items/?page=1&per_page=20
Authorization: Bearer <your-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total": 4,
    "page": 1,
    "per_page": 20,
    "items": [
      {
        "id": 1,
        "icon_name": "trophy",
        "achievement_value": "100+",
        "title": {
          "en": "Projects Completed",
          "ar": "مشروع مكتمل"
        },
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-20T14:45:00Z",
        "updated_by": {
          "id": 1,
          "email": "<EMAIL>",
          "first_name": "Admin",
          "last_name": "User"
        }
      }
    ]
  }
}
```

#### Create Achievement Item
```http
POST /api/admin/home-page/achievements/items/
Authorization: Bearer <your-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "icon_name": "users",
  "achievement_value": "500+",
  "title": {
    "en": "Happy Clients",
    "ar": "عميل سعيد"
  }
}
```

#### Get Specific Achievement Item
```http
GET /api/admin/home-page/achievements/items/{id}/
Authorization: Bearer <your-token>
```

#### Update Achievement Item
```http
PUT /api/admin/home-page/achievements/items/{id}/
Authorization: Bearer <your-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "icon_name": "trophy",
  "achievement_value": "150+",
  "title": {
    "en": "Projects Completed",
    "ar": "مشروع مكتمل"
  }
}
```

#### Delete Achievement Item
```http
DELETE /api/admin/home-page/achievements/items/{id}/
Authorization: Bearer <your-token>
```

---

## Public Endpoints (No Authentication Required)

### Get Achievement Section (English)
```http
GET /api/home-page/achievements/en/
```

**Response:**
```json
{
  "success": true,
  "message": "Achievements content retrieved successfully",
  "data": {
    "id": 1,
    "badge": "Our Achievements",
    "title": "What We Have Accomplished",
    "supporting_text": "Over the years, we have achieved remarkable milestones.",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z"
  }
}
```

### Get Achievement Section (Arabic)
```http
GET /api/home-page/achievements/ar/
```

### Get Achievement Items (English)
```http
GET /api/home-page/achievements/items/en/
```

**Response:**
```json
{
  "success": true,
  "message": "Achievement items retrieved successfully",
  "data": [
    {
      "id": 1,
      "icon_name": "trophy",
      "achievement_value": "100+",
      "title": "Projects Completed",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-20T14:45:00Z"
    },
    {
      "id": 2,
      "icon_name": "users",
      "achievement_value": "500+",
      "title": "Happy Clients",
      "created_at": "2024-01-15T10:35:00Z",
      "updated_at": "2024-01-20T14:50:00Z"
    }
  ]
}
```

### Get Achievement Items (Arabic)
```http
GET /api/home-page/achievements/items/ar/
```

---

## CURL Test Examples

### 1. Get Achievement Section (Admin)
```bash
curl -X GET "http://localhost:8000/api/admin/home-page/achievements/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json"
```

### 2. Update Achievement Section
```bash
curl -X PUT "http://localhost:8000/api/admin/home-page/achievements/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "badge": {
      "en": "Our Achievements",
      "ar": "إنجازاتنا"
    },
    "title": {
      "en": "What We Have Accomplished",
      "ar": "ما حققناه"
    },
    "supporting_text": {
      "en": "Over the years, we have achieved remarkable milestones.",
      "ar": "على مر السنين، حققنا إنجازات رائعة."
    }
  }'
```

### 3. Create Achievement Item
```bash
curl -X POST "http://localhost:8000/api/admin/home-page/achievements/items/" \
  -H "Authorization: Bearer your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "icon_name": "star",
    "achievement_value": "5 Years",
    "title": {
      "en": "Experience",
      "ar": "خبرة"
    }
  }'
```

### 4. Get Public Achievement Section (English)
```bash
curl -X GET "http://localhost:8000/api/home-page/achievements/en/"
```

### 5. Get Public Achievement Items (English)
```bash
curl -X GET "http://localhost:8000/api/home-page/achievements/items/en/"
```

### 6. Get Public Achievement Items (Arabic)
```bash
curl -X GET "http://localhost:8000/api/home-page/achievements/items/ar/"
```

---

## Error Responses

### 400 Bad Request - Validation Error
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "title": {
      "en": ["This field is required"]
    }
  },
  "error_code": "VALIDATION_ERROR"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication credentials were not provided.",
  "error_code": "AUTHENTICATION_REQUIRED"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Permission denied. You don't have access to manage home page content.",
  "error_code": "PERMISSION_DENIED"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Achievement item not found",
  "error_code": "ACHIEVEMENT_ITEM_NOT_FOUND"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error occurred",
  "error_code": "INTERNAL_ERROR"
}
```

---

## Features

- ✅ **Full CRUD operations** for both sections and items
- ✅ **Bilingual support** (English/Arabic)
- ✅ **Revision tracking** with change history
- ✅ **Permission-based access control**
- ✅ **Pagination** for lists
- ✅ **Soft delete** for items
- ✅ **Comprehensive validation**
- ✅ **Admin interface** for easy management
- ✅ **Public API endpoints** for frontend consumption

## Expected Test Results

### Create Achievement Item - Expected Response
```json
{
  "success": true,
  "message": "Achievement item created successfully",
  "data": {
    "id": 5,
    "icon_name": "star",
    "achievement_value": "5 Years",
    "title": {
      "en": "Experience",
      "ar": "خبرة"
    },
    "created_at": "2024-01-20T16:00:00Z",
    "updated_at": "2024-01-20T16:00:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User"
    }
  }
}
```

### Update Achievement Item - Expected Response
```json
{
  "success": true,
  "message": "Achievement item updated successfully",
  "data": {
    "id": 1,
    "icon_name": "trophy",
    "achievement_value": "150+",
    "title": {
      "en": "Projects Completed",
      "ar": "مشروع مكتمل"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T16:30:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User"
    }
  }
}
```

### Delete Achievement Item - Expected Response
```json
{
  "success": true,
  "message": "Achievement item deleted successfully"
}
```

### Get Achievement Section History - Expected Response
```json
{
  "success": true,
  "data": {
    "total": 5,
    "page": 1,
    "per_page": 10,
    "revisions": [
      {
        "id": 3,
        "changes": {
          "title": {
            "old": {
              "en": "Old Title",
              "ar": "العنوان القديم"
            },
            "new": {
              "en": "What We Have Accomplished",
              "ar": "ما حققناه"
            }
          }
        },
        "updated_by": {
          "id": 1,
          "email": "<EMAIL>",
          "first_name": "Admin",
          "last_name": "User"
        },
        "updated_at": "2024-01-20T15:00:00Z"
      }
    ]
  }
}
```

## Common Icon Names

Suggested icon names for achievement items:
- `trophy` - For awards and accomplishments
- `users` - For client/user counts
- `star` - For ratings and quality
- `calendar` - For years of experience
- `check-circle` - For completed projects
- `heart` - For satisfaction rates
- `globe` - For global reach
- `shield` - For security/trust
- `rocket` - For growth/innovation
- `thumbs-up` - For approval ratings

## Common Achievement Values

Examples of achievement values:
- `100+` - For counts over 100
- `5 Years` - For experience duration
- `99%` - For percentage achievements
- `24/7` - For availability
- `1M+` - For large numbers
- `#1` - For rankings
- `500K` - For abbreviated large numbers

## Notes

- Icon names should be valid icon identifiers (e.g., "trophy", "users", "star")
- Achievement values should be numeric with symbols (e.g., "100+", "5 Years", "99%")
- All admin endpoints require proper authentication and permissions
- Public endpoints are optimized for frontend consumption
- Revision history tracks all changes for audit purposes
- Soft delete preserves data integrity while hiding items from public view
- All timestamps are in ISO 8601 format (UTC)

# Generated by Django 5.2.1 on 2025-05-26 05:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AboutUsSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('badge_en', models.CharField(max_length=100, verbose_name='Badge (English)')),
                ('badge_ar', models.CharField(max_length=100, verbose_name='Badge (Arabic)')),
                ('title_en', models.CharField(max_length=50, verbose_name='Title (English)')),
                ('title_ar', models.CharField(max_length=50, verbose_name='Title (Arabic)')),
                ('subtitle_en', models.Char<PERSON>ield(max_length=50, verbose_name='Subtitle (English)')),
                ('subtitle_ar', models.Char<PERSON><PERSON>(max_length=50, verbose_name='Subtitle (Arabic)')),
                ('description_en', models.TextField(max_length=1000, verbose_name='Description (English)')),
                ('description_ar', models.TextField(max_length=1000, verbose_name='Description (Arabic)')),
                ('image_overlay_badge_en', models.CharField(max_length=50, verbose_name='Image Overlay Badge (English)')),
                ('image_overlay_badge_ar', models.CharField(max_length=50, verbose_name='Image Overlay Badge (Arabic)')),
                ('image_overlay_text_en', models.CharField(max_length=100, verbose_name='Image Overlay Text (English)')),
                ('image_overlay_text_ar', models.CharField(max_length=100, verbose_name='Image Overlay Text (Arabic)')),
                ('main_image', models.CharField(default='/images/home/<USER>', max_length=255, verbose_name='Main Image URL')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='about_us_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'About Us Section',
                'verbose_name_plural': 'About Us Sections',
                'db_table': 'homepage_about_us_section',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='AboutUsImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image_url', models.CharField(max_length=255, verbose_name='Image URL')),
                ('alt_text_en', models.CharField(blank=True, max_length=255, verbose_name='Alt Text (English)')),
                ('alt_text_ar', models.CharField(blank=True, max_length=255, verbose_name='Alt Text (Arabic)')),
                ('file_size', models.IntegerField(blank=True, null=True, verbose_name='File Size (bytes)')),
                ('width', models.IntegerField(blank=True, null=True, verbose_name='Image Width')),
                ('height', models.IntegerField(blank=True, null=True, verbose_name='Image Height')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('about_us_section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='homepage.aboutussection')),
            ],
            options={
                'verbose_name': 'About Us Image',
                'verbose_name_plural': 'About Us Images',
                'db_table': 'homepage_about_us_images',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='AboutUsSectionRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('changes', models.JSONField(verbose_name='Changes Made')),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('about_us_section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions', to='homepage.aboutussection')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'About Us Section Revision',
                'verbose_name_plural': 'About Us Section Revisions',
                'db_table': 'homepage_about_us_revisions',
                'ordering': ['-updated_at'],
            },
        ),
    ]

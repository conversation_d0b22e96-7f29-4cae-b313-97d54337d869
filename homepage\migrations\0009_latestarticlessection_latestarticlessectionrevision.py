# Generated by Django 5.2.1 on 2025-06-04 14:10

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0008_fix_achievements_models_structure'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LatestArticlesSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('badge_en', models.CharField(max_length=100, verbose_name='Badge (English)')),
                ('badge_ar', models.CharField(max_length=100, verbose_name='Badge (Arabic)')),
                ('title_en', models.CharField(max_length=100, verbose_name='Title (English)')),
                ('title_ar', models.Char<PERSON>ield(max_length=100, verbose_name='Title (Arabic)')),
                ('description_en', models.TextField(max_length=500, verbose_name='Description (English)')),
                ('description_ar', models.TextField(max_length=500, verbose_name='Description (Arabic)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='latest_articles_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Latest Articles Section',
                'verbose_name_plural': 'Latest Articles Sections',
                'db_table': 'homepage_latest_articles_section',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='LatestArticlesSectionRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('changes', models.JSONField(verbose_name='Changes Made')),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('latest_articles_section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions', to='homepage.latestarticlessection')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Latest Articles Section Revision',
                'verbose_name_plural': 'Latest Articles Section Revisions',
                'db_table': 'homepage_latest_articles_revisions',
                'ordering': ['-updated_at'],
            },
        ),
    ]

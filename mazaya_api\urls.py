from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from django.http import JsonResponse
from homepage.views import (
    get_hero_content_en, get_hero_content_ar,
    get_about_us_content_en, get_about_us_content_ar,
    get_featured_projects_content_en, get_featured_projects_content_ar,
    get_achievements_content_en, get_achievements_content_ar,
    get_achievements_items_en, get_achievements_items_ar,
    get_latest_articles_content_en, get_latest_articles_content_ar,
    get_testimonials_content_en, get_testimonials_content_ar
)

def api_root(request):
    return JsonResponse({"message": "Welcome to Mazaya API"})

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('api.urls')),
    path('api/admin/home-page/', include('homepage.urls')),
    # Public homepage endpoints (no authentication required)
    path('api/home-page/hero/en/', get_hero_content_en, name='hero-content-en'),
    path('api/home-page/hero/ar/', get_hero_content_ar, name='hero-content-ar'),
    path('api/home-page/about-us/en/', get_about_us_content_en, name='about-us-content-en'),
    path('api/home-page/about-us/ar/', get_about_us_content_ar, name='about-us-content-ar'),
    path('api/home-page/featured-projects/en/', get_featured_projects_content_en, name='featured-projects-content-en'),
    path('api/home-page/featured-projects/ar/', get_featured_projects_content_ar, name='featured-projects-content-ar'),
    path('api/home-page/achievements/en/', get_achievements_content_en, name='achievements-content-en'),
    path('api/home-page/achievements/ar/', get_achievements_content_ar, name='achievements-content-ar'),
    path('api/home-page/achievements/items/en/', get_achievements_items_en, name='achievements-items-en'),
    path('api/home-page/achievements/items/ar/', get_achievements_items_ar, name='achievements-items-ar'),
    path('api/home-page/latest-articles/en/', get_latest_articles_content_en, name='latest-articles-content-en'),
    path('api/home-page/latest-articles/ar/', get_latest_articles_content_ar, name='latest-articles-content-ar'),
    path('api/home-page/testimonials/en/', get_testimonials_content_en, name='testimonials-content-en'),
    path('api/home-page/testimonials/ar/', get_testimonials_content_ar, name='testimonials-content-ar'),
    path('', api_root, name='api-root'),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

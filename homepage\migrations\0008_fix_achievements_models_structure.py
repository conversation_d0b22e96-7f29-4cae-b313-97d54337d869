# Generated by Django 5.2.1 on 2025-06-04 00:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0007_fix_achievements_models'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='achievementsitem',
            name='achievement_value_ar',
        ),
        migrations.RemoveField(
            model_name='achievementsitem',
            name='achievement_value_en',
        ),
        migrations.RemoveField(
            model_name='achievementsitem',
            name='icon_name_ar',
        ),
        migrations.RemoveField(
            model_name='achievementsitem',
            name='icon_name_en',
        ),
        migrations.RemoveField(
            model_name='achievementssection',
            name='description_ar',
        ),
        migrations.RemoveField(
            model_name='achievementssection',
            name='description_en',
        ),
        migrations.AddField(
            model_name='achievementsitem',
            name='achievement_value',
            field=models.CharField(default='100+', max_length=50, verbose_name='Achievement Value'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='achievementsitem',
            name='icon_name',
            field=models.CharField(default='trophy', max_length=100, verbose_name='Icon Name'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='achievementssection',
            name='supporting_text_ar',
            field=models.TextField(default='نص داعم', max_length=500, verbose_name='Supporting Text (Arabic)'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='achievementssection',
            name='supporting_text_en',
            field=models.TextField(default='Supporting text', max_length=500, verbose_name='Supporting Text (English)'),
            preserve_default=False,
        ),
    ]

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from homepage.models import AboutUsSection

User = get_user_model()


class Command(BaseCommand):
    help = 'Create initial About Us section content'

    def handle(self, *args, **options):
        # Check if About Us content already exists
        if AboutUsSection.objects.filter(is_active=True).exists():
            self.stdout.write(
                self.style.WARNING('About Us content already exists. Skipping creation.')
            )
            return

        # Get or create admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.filter(email='<EMAIL>').first()

        # Create initial About Us content
        about_us_content = AboutUsSection.objects.create(
            # Badge content
            badge_en="About Mazaya Capital",
            badge_ar="حول مزايا كابيتال",
            
            # Title content
            title_en="Welcome to",
            title_ar="مرحبا بك في",
            
            # Subtitle content
            subtitle_en="Mazaya Capital",
            subtitle_ar="مزايا كابيتال",
            
            # Description content
            description_en="A leading real estate development company specializing in premium properties across the UAE. With a focus on innovation, quality, and sustainability, we transform visions into exceptional developments.",
            description_ar="مزايا كابيتال هي شركة تطوير عقاري رائدة تخصصت في إنشاء منازل ومنشآت تجارية ومشاريع متعددة الاستخدامات، تأتي بقيمة استثنائية للمستثمرين والمقيمين.",
            
            # Image overlay content
            image_overlay_badge_en="Architectural Excellence",
            image_overlay_badge_ar="التميز المعماري",
            image_overlay_text_en="Shaping Iconic Landmarks Since 2005",
            image_overlay_text_ar="نشكل المعالم الأيقونية منذ عام ٢٠٠٥",
            
            # Main image (using URL by default)
            main_image_url="/images/home/<USER>",
            
            # Image alt text
            image_alt_text_en="Mazaya Capital office building showcasing modern architecture",
            image_alt_text_ar="مبنى مكاتب مزايا كابيتال يعرض العمارة الحديثة",
            
            # Metadata
            is_active=True,
            updated_by=admin_user
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created initial About Us content with ID: {about_us_content.id}'
            )
        )
        
        # Display the created content
        self.stdout.write("\n" + "="*50)
        self.stdout.write("CREATED ABOUT US CONTENT:")
        self.stdout.write("="*50)
        self.stdout.write(f"Badge EN: {about_us_content.badge_en}")
        self.stdout.write(f"Badge AR: {about_us_content.badge_ar}")
        self.stdout.write(f"Title EN: {about_us_content.title_en}")
        self.stdout.write(f"Title AR: {about_us_content.title_ar}")
        self.stdout.write(f"Subtitle EN: {about_us_content.subtitle_en}")
        self.stdout.write(f"Subtitle AR: {about_us_content.subtitle_ar}")
        self.stdout.write(f"Description EN: {about_us_content.description_en[:100]}...")
        self.stdout.write(f"Description AR: {about_us_content.description_ar[:100]}...")
        self.stdout.write(f"Image Overlay Badge EN: {about_us_content.image_overlay_badge_en}")
        self.stdout.write(f"Image Overlay Badge AR: {about_us_content.image_overlay_badge_ar}")
        self.stdout.write(f"Image Overlay Text EN: {about_us_content.image_overlay_text_en}")
        self.stdout.write(f"Image Overlay Text AR: {about_us_content.image_overlay_text_ar}")
        self.stdout.write(f"Main Image URL: {about_us_content.main_image_url}")
        self.stdout.write(f"Main Image File: {about_us_content.main_image_file}")
        self.stdout.write(f"Main Image (computed): {about_us_content.main_image}")
        self.stdout.write(f"Image Alt Text EN: {about_us_content.image_alt_text_en}")
        self.stdout.write(f"Image Alt Text AR: {about_us_content.image_alt_text_ar}")
        self.stdout.write(f"Created at: {about_us_content.created_at}")
        self.stdout.write(f"Updated by: {about_us_content.updated_by}")
        self.stdout.write("="*50) 
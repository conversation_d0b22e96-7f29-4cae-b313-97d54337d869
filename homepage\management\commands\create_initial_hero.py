from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from homepage.models import HeroSection

User = get_user_model()


class Command(BaseCommand):
    help = 'Create initial hero section content'

    def handle(self, *args, **options):
        # Check if hero section already exists
        if HeroSection.objects.filter(is_active=True).exists():
            self.stdout.write(
                self.style.WARNING('Hero section already exists. Skipping creation.')
            )
            return

        # Get or create admin user
        try:
            admin_user = User.objects.filter(role='Super Admin').first()
            if not admin_user:
                admin_user = User.objects.filter(is_superuser=True).first()
        except:
            admin_user = None

        # Create initial hero section
        hero_section = HeroSection.objects.create(
            # Badge
            badge_en="Redefining Architectural Excellence",
            badge_ar="إعادة تعريف التميز المعماري",
            
            # Title
            title_line1_en="We Shape",
            title_line1_ar="نحن نشكل",
            title_line2_en="Tomorrow's",
            title_line2_ar="خطوط",
            title_line3_en="Skylines",
            title_line3_ar="المستقبل",
            
            # Description
            description_en="Mazaya Capital combines architectural innovation with premium investment opportunities to create landmark developments that define the future.",
            description_ar="مزايا كابيتال تجمع بين الابتكار المعماري وفرص الاستثمار المتميزة لإنشاء تطويرات بارزة تحدد المستقبل.",
            
            # Primary Button
            primary_button_text_en="Explore Our Projects",
            primary_button_text_ar="استكشف مشاريعنا",
            primary_button_link="/projects",
            
            # Secondary Button
            secondary_button_text_en="Contact Us",
            secondary_button_text_ar="تواصل معنا",
            secondary_button_link="/contact",
            
            # Bottom Text
            bottom_text_en="Architecting Excellence Since 2005",
            bottom_text_ar="نصمم التميز منذ عام ٢٠٠٥",
            
            # Metadata
            is_active=True,
            updated_by=admin_user
        )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created hero section with ID: {hero_section.id}')
        ) 
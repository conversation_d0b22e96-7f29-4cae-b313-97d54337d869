# Generated by Django 5.2.1 on 2025-05-26 04:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('badge_en', models.Char<PERSON>ield(max_length=200, verbose_name='Badge (English)')),
                ('badge_ar', models.CharField(max_length=200, verbose_name='<PERSON><PERSON> (Arabic)')),
                ('title_line1_en', models.CharField(max_length=100, verbose_name='Title Line 1 (English)')),
                ('title_line1_ar', models.CharField(max_length=100, verbose_name='Title Line 1 (Arabic)')),
                ('title_line2_en', models.Char<PERSON>ield(max_length=100, verbose_name='Title Line 2 (English)')),
                ('title_line2_ar', models.CharField(max_length=100, verbose_name='Title Line 2 (Arabic)')),
                ('title_line3_en', models.CharField(max_length=100, verbose_name='Title Line 3 (English)')),
                ('title_line3_ar', models.CharField(max_length=100, verbose_name='Title Line 3 (Arabic)')),
                ('description_en', models.TextField(max_length=500, verbose_name='Description (English)')),
                ('description_ar', models.TextField(max_length=500, verbose_name='Description (Arabic)')),
                ('primary_button_text_en', models.CharField(max_length=50, verbose_name='Primary Button Text (English)')),
                ('primary_button_text_ar', models.CharField(max_length=50, verbose_name='Primary Button Text (Arabic)')),
                ('primary_button_link', models.CharField(max_length=200, verbose_name='Primary Button Link')),
                ('secondary_button_text_en', models.CharField(max_length=50, verbose_name='Secondary Button Text (English)')),
                ('secondary_button_text_ar', models.CharField(max_length=50, verbose_name='Secondary Button Text (Arabic)')),
                ('secondary_button_link', models.CharField(max_length=200, verbose_name='Secondary Button Link')),
                ('bottom_text_en', models.CharField(max_length=100, verbose_name='Bottom Text (English)')),
                ('bottom_text_ar', models.CharField(max_length=100, verbose_name='Bottom Text (Arabic)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hero_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Hero Section',
                'verbose_name_plural': 'Hero Sections',
                'db_table': 'homepage_hero_section',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='HeroSectionRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('changes', models.JSONField(verbose_name='Changes Made')),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('hero_section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions', to='homepage.herosection')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Hero Section Revision',
                'verbose_name_plural': 'Hero Section Revisions',
                'db_table': 'homepage_hero_revisions',
                'ordering': ['-updated_at'],
            },
        ),
    ]

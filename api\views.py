from django.shortcuts import render
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from django.contrib.auth import login
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from datetime import timedelta
import secrets
import string

from .models import AdminUser, PasswordResetToken
from .serializers import (
    AdminLoginSerializer, 
    AdminUserSerializer, 
    TokenRefreshSerializer,
    PasswordResetRequestSerializer,
    LogoutSerializer,
    AdminUserProfileSerializer,
    AdminUserProfileUpdateSerializer,
    AvatarUploadSerializer
)

# Create your views here.
@api_view(['GET'])
@permission_classes([AllowAny])
def api_overview(request):
    """
    API overview endpoint
    """
    api_urls = {
        'Admin Auth': {
            'Login': '/api/admin/auth/login/',
            'Logout': '/api/admin/auth/logout/',
            'Refresh Token': '/api/admin/auth/refresh/',
            'Password Reset': '/api/admin/auth/password-reset/',
            'User Profile': '/api/admin/auth/me/',
            'Update Profile': '/api/admin/auth/profile/',
            'Upload Avatar': '/api/admin/auth/avatar/ (POST)',
            'Delete Avatar': '/api/admin/auth/avatar/ (DELETE)',
        },
        'Homepage Management': {
            'Get Hero Content': '/api/admin/home-page/hero/ (GET)',
            'Update Hero Content': '/api/admin/home-page/hero/ (PUT/PATCH)',
            'Update Hero Section': '/api/admin/home-page/hero/section/ (PATCH)',
            'Hero History': '/api/admin/home-page/hero/history/ (GET)',
        }
    }
    return Response(api_urls)


@api_view(['POST'])
@permission_classes([AllowAny])
def admin_login(request):
    """
    Admin login endpoint
    POST /api/admin/auth/login/
    """
    serializer = AdminLoginSerializer(data=request.data)
    
    if serializer.is_valid():
        user = serializer.validated_data['user']
        remember_me = serializer.validated_data.get('remember_me', False)
        
        # Update last login
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        # Adjust token lifetime based on remember_me
        if remember_me:
            refresh.set_exp(lifetime=timedelta(days=30))
            access_token.set_exp(lifetime=timedelta(hours=24))
        
        # Calculate session expiry
        session_expires = timezone.now() + timedelta(days=30 if remember_me else 7)
        
        # Serialize user data
        user_serializer = AdminUserSerializer(user)
        
        return Response({
            'success': True,
            'message': 'Login successful',
            'data': {
                'user': user_serializer.data,
                'tokens': {
                    'access': str(access_token),
                    'refresh': str(refresh),
                },
                'session': {
                    'expires_at': session_expires.isoformat(),
                    'remember_me': remember_me
                }
            }
        }, status=status.HTTP_200_OK)
    
    return Response({
        'success': False,
        'message': 'Invalid credentials',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def admin_refresh_token(request):
    """
    Token refresh endpoint
    POST /api/admin/auth/refresh/
    """
    serializer = TokenRefreshSerializer(data=request.data)
    
    if serializer.is_valid():
        refresh_token = serializer.validated_data['refresh']
        
        try:
            refresh = RefreshToken(refresh_token)
            access_token = refresh.access_token
            
            return Response({
                'success': True,
                'data': {
                    'access': str(access_token),
                    'expires_at': (timezone.now() + timedelta(hours=1)).isoformat()
                }
            }, status=status.HTTP_200_OK)
            
        except TokenError as e:
            return Response({
                'success': False,
                'message': 'Invalid or expired refresh token',
                'errors': {'refresh': [str(e)]}
            }, status=status.HTTP_401_UNAUTHORIZED)
    
    return Response({
        'success': False,
        'message': 'Invalid request',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def admin_logout(request):
    """
    Admin logout endpoint
    POST /api/admin/auth/logout/
    """
    serializer = LogoutSerializer(data=request.data)
    
    if serializer.is_valid():
        refresh_token = serializer.validated_data['refresh']
        
        try:
            token = RefreshToken(refresh_token)
            token.blacklist()
            
            return Response({
                'success': True,
                'message': 'Successfully logged out'
            }, status=status.HTTP_200_OK)
            
        except TokenError:
            return Response({
                'success': False,
                'message': 'Invalid refresh token'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    return Response({
        'success': False,
        'message': 'Invalid request',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def admin_password_reset(request):
    """
    Password reset request endpoint
    POST /api/admin/auth/password-reset/
    """
    serializer = PasswordResetRequestSerializer(data=request.data)
    
    if serializer.is_valid():
        email = serializer.validated_data['email']
        
        try:
            user = AdminUser.objects.get(email=email, is_active=True)
            
            # Generate reset token
            token = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(64))
            
            # Create or update password reset token
            reset_token, created = PasswordResetToken.objects.get_or_create(
                user=user,
                defaults={
                    'token': token,
                    'expires_at': timezone.now() + timedelta(hours=1),
                    'is_used': False
                }
            )
            
            if not created:
                # Update existing token
                reset_token.token = token
                reset_token.expires_at = timezone.now() + timedelta(hours=1)
                reset_token.is_used = False
                reset_token.save()
            
            # TODO: Send email with reset link
            # For now, we'll just log it to console (since EMAIL_BACKEND is console)
            reset_link = f"http://localhost:3000/admin/reset-password?token={token}"
            
            try:
                send_mail(
                    subject='Password Reset Request - Mazaya Capital',
                    message=f'Click the following link to reset your password: {reset_link}',
                    from_email=settings.EMAIL_HOST_USER or '<EMAIL>',
                    recipient_list=[email],
                    fail_silently=False,
                )
            except Exception as e:
                print(f"Email sending failed: {e}")
            
        except AdminUser.DoesNotExist:
            # Don't reveal if email exists or not for security
            pass
        
        # Always return success to prevent email enumeration
        return Response({
            'success': True,
            'message': 'If the email exists, a password reset link has been sent.'
        }, status=status.HTTP_200_OK)
    
    return Response({
        'success': False,
        'message': 'Invalid request',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_profile(request):
    """
    Get admin user profile
    GET /api/admin/auth/me/
    """
    serializer = AdminUserProfileSerializer(request.user)
    
    return Response({
        'success': True,
        'data': serializer.data
    }, status=status.HTTP_200_OK)


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def admin_profile_update(request):
    """
    Update admin user profile
    PUT /api/admin/auth/profile/
    """
    user = request.user
    serializer = AdminUserProfileUpdateSerializer(user, data=request.data, partial=True)
    
    if serializer.is_valid():
        serializer.save()
        
        return Response({
            'success': True,
            'message': 'Profile updated successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
    
    return Response({
        'success': False,
        'message': 'Invalid data provided',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def admin_avatar_upload(request):
    """
    Upload/Update admin user avatar
    POST /api/admin/auth/avatar/
    """
    user = request.user
    
    # Check if avatar file is provided
    if 'avatar' not in request.FILES:
        return Response({
            'success': False,
            'message': 'No file provided',
            'errors': {
                'avatar': ['Avatar file is required.']
            }
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Check file size before processing (5MB limit)
    avatar_file = request.FILES['avatar']
    if avatar_file.size > 5 * 1024 * 1024:
        return Response({
            'success': False,
            'message': 'File too large',
            'errors': {
                'avatar': ['File size must be less than 5MB.']
            }
        }, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)
    
    serializer = AvatarUploadSerializer(user, data=request.data, partial=True)
    
    if serializer.is_valid():
        updated_user = serializer.save()
        
        # Get the avatar URL
        avatar_url = updated_user.avatar.url if updated_user.avatar else '/media/avatars/admin.jpg'
        
        # Serialize complete user data
        user_serializer = AdminUserSerializer(updated_user)
        
        return Response({
            'success': True,
            'message': 'Avatar updated successfully',
            'data': {
                'avatar': avatar_url,
                'user': user_serializer.data
            }
        }, status=status.HTTP_200_OK)
    
    # Handle validation errors
    errors = serializer.errors
    if 'avatar' in errors:
        error_message = errors['avatar'][0]
        if 'Only JPEG, PNG, and GIF files are allowed.' in error_message:
            return Response({
                'success': False,
                'message': 'Invalid file format',
                'errors': {
                    'avatar': ['Only JPEG, PNG, and GIF files are allowed.']
                }
            }, status=status.HTTP_400_BAD_REQUEST)
        elif 'File size must be less than 5MB.' in error_message:
            return Response({
                'success': False,
                'message': 'File too large',
                'errors': {
                    'avatar': ['File size must be less than 5MB.']
                }
            }, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)
    
    return Response({
        'success': False,
        'message': 'Invalid avatar file',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def admin_avatar_delete(request):
    """
    Delete admin user avatar
    DELETE /api/admin/auth/avatar/
    """
    user = request.user
    
    if user.avatar:
        user.avatar.delete(save=True)
        
        return Response({
            'success': True,
            'message': 'Avatar deleted successfully',
            'data': {
                'avatar_url': '/media/avatars/admin.jpg'  # Default avatar
            }
        }, status=status.HTTP_200_OK)
    
    return Response({
        'success': False,
        'message': 'No avatar to delete'
    }, status=status.HTTP_400_BAD_REQUEST)

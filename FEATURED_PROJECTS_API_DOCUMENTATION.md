# Featured Projects API Documentation

## Overview
This document provides comprehensive documentation for the Featured Projects section API endpoints. The implementation follows the **exact same pattern** as the existing **Hero section** endpoints, ensuring consistency and compatibility with the frontend.

## Endpoint Details

### 1. GET Featured Projects Content
**GET** `/api/admin/home-page/featured-projects/`

Fetch current featured projects section content.

#### Authentication
- **Required**: Yes
- **Type**: <PERSON><PERSON> (JWT)
- **Header**: `Authorization: Bearer <access_token>`

#### Permissions
- User must have homepage management permissions
- Super Admin, Admin, or staff users with appropriate permissions

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `lang` | String | No | Language preference (`en` or `ar`) |

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Featured projects content retrieved successfully",
  "data": {
    "id": 1,
    "title": {
      "en": "Featured Projects",
      "ar": "المشاريع المميزة"
    },
    "subtitle": {
      "en": "Exceptional Developments",
      "ar": "تطويرات استثنائية"
    },
    "description": {
      "en": "Discover our exceptional real estate developments with premium locations and world-class amenities",
      "ar": "اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى"
    },
    "button": {
      "text": {
        "en": "View All Projects",
        "ar": "عرض جميع المشاريع"
      },
      "link": "/projects"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

#### Error Responses

##### 404 Not Found - Content Not Found
```json
{
  "success": false,
  "message": "Featured projects content not found",
  "error_code": "FEATURED_PROJECTS_NOT_FOUND"
}
```

##### 403 Forbidden - Insufficient Permissions
```json
{
  "success": false,
  "message": "Permission denied. You don't have access to manage home page content.",
  "error_code": "PERMISSION_DENIED"
}
```

---

### 2. PUT/PATCH Featured Projects Content
**PUT** `/api/admin/home-page/featured-projects/`
**PATCH** `/api/admin/home-page/featured-projects/`

Update all featured projects section content.

#### Authentication
- **Required**: Yes
- **Type**: Bearer Token (JWT)
- **Header**: `Authorization: Bearer <access_token>`

#### Request Body
```json
{
  "title": {
    "en": "Featured Projects",
    "ar": "المشاريع المميزة"
  },
  "subtitle": {
    "en": "Exceptional Developments",
    "ar": "تطويرات استثنائية"
  },
  "description": {
    "en": "Discover our exceptional real estate developments with premium locations and world-class amenities",
    "ar": "اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى"
  },
  "button": {
    "text": {
      "en": "View All Projects",
      "ar": "عرض جميع المشاريع"
    },
    "link": "/projects"
  }
}
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Featured projects section updated successfully",
  "data": {
    "id": 1,
    "title": {
      "en": "Featured Projects",
      "ar": "المشاريع المميزة"
    },
    "subtitle": {
      "en": "Exceptional Developments", 
      "ar": "تطويرات استثنائية"
    },
    "description": {
      "en": "Discover our exceptional real estate developments with premium locations and world-class amenities",
      "ar": "اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى"
    },
    "button": {
      "text": {
        "en": "View All Projects",
        "ar": "عرض جميع المشاريع"
      },
      "link": "/projects"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>", 
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

#### Validation Error Response (400 Bad Request)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "title.en": ["This field is required"],
    "title.ar": ["This field is required"],
    "button.link": ["Enter a valid URL"]
  },
  "error_code": "VALIDATION_ERROR"
}
```

---

### 3. PATCH Featured Projects Section
**PATCH** `/api/admin/home-page/featured-projects/section/`

Update specific section of featured projects content.

#### Authentication
- **Required**: Yes
- **Type**: Bearer Token (JWT)
- **Header**: `Authorization: Bearer <access_token>`

#### Request Body
```json
{
  "section": "title",
  "data": {
    "en": "Updated Featured Projects",
    "ar": "المشاريع المميزة المحدثة"
  }
}
```

#### Valid Section Names
- `title` - Section title
- `subtitle` - Section subtitle  
- `description` - Section description
- `button` - Call-to-action button

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Title section updated successfully",
  "data": {
    "section": "title",
    "updated_data": {
      "en": "Updated Featured Projects",
      "ar": "المشاريع المميزة المحدثة"
    },
    "updated_at": "2024-01-20T14:45:00Z"
  }
}
```

---

### 4. GET Featured Projects History
**GET** `/api/admin/home-page/featured-projects/history/`

Get revision history for featured projects section.

#### Authentication
- **Required**: Yes
- **Type**: Bearer Token (JWT)
- **Header**: `Authorization: Bearer <access_token>`

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | Integer | No | Page number (default: 1) |
| `per_page` | Integer | No | Items per page (default: 10, max: 50) |

#### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "total": 15,
    "page": 1,
    "per_page": 10,
    "revisions": [
      {
        "id": 5,
        "changes": {
          "title": {
            "old": {
              "en": "Old Title",
              "ar": "العنوان القديم"
            },
            "new": {
              "en": "New Title",
              "ar": "العنوان الجديد"
            }
          }
        },
        "updated_by": {
          "id": 1,
          "email": "<EMAIL>",
          "first_name": "John",
          "last_name": "Doe"
        },
        "updated_at": "2024-01-20T14:45:00Z"
      }
    ]
  }
}
```

---

## Database Model

### Django Model Structure
```python
class FeaturedProjectsSection(models.Model):
    # Title fields
    title_en = models.CharField(max_length=100, verbose_name="Title (English)")
    title_ar = models.CharField(max_length=100, verbose_name="Title (Arabic)")
    
    # Subtitle fields  
    subtitle_en = models.CharField(max_length=150, blank=True, verbose_name="Subtitle (English)")
    subtitle_ar = models.CharField(max_length=150, blank=True, verbose_name="Subtitle (Arabic)")
    
    # Description fields
    description_en = models.TextField(max_length=500, verbose_name="Description (English)")
    description_ar = models.TextField(max_length=500, verbose_name="Description (Arabic)")
    
    # Button fields
    button_text_en = models.CharField(max_length=50, verbose_name="Button Text (English)")
    button_text_ar = models.CharField(max_length=50, verbose_name="Button Text (Arabic)")
    button_link = models.CharField(max_length=200, verbose_name="Button Link")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    is_active = models.BooleanField(default=True)
```

---

## Validation Rules

### Title Fields
- **Required**: Yes (both EN/AR)
- **Max Length**: 100 characters each
- **Type**: String

### Subtitle Fields
- **Required**: No  
- **Max Length**: 150 characters each
- **Type**: String

### Description Fields
- **Required**: Yes (both EN/AR)
- **Max Length**: 500 characters each
- **Type**: Text

### Button Fields
- **Text Required**: Yes (both EN/AR)
- **Text Max Length**: 50 characters each
- **Link Required**: Yes
- **Link Type**: Valid URL or relative path
- **Link Format**: Must be valid URL format

---

## Implementation Features

### Pattern Consistency
This implementation follows the **exact same pattern** as the Hero section:

1. **Model Structure**: Same field naming conventions and metadata
2. **Serializer Pattern**: Nested structure with bilingual support
3. **View Functions**: Same permission checking and error handling
4. **URL Patterns**: Consistent endpoint naming
5. **Response Format**: Identical success/error response structure
6. **Revision Tracking**: Complete change history with user attribution

### Key Features
- **Bilingual Support**: Full English/Arabic content support
- **Revision Tracking**: Complete change history
- **Permission-Based Access**: Role-based access control
- **Comprehensive Validation**: Field-level validation with proper error messages
- **Section Updates**: Granular section-specific updates
- **Pagination**: History endpoint with pagination support

### Frontend Integration
Since this follows the exact same pattern as Hero section, you can use the same frontend approach:

```javascript
// GET Featured Projects Content
const response = await fetch('/api/admin/home-page/featured-projects/', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  }
});

// PUT Update Featured Projects Content
const updateResponse = await fetch('/api/admin/home-page/featured-projects/', {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: { en: "New Title", ar: "عنوان جديد" },
    subtitle: { en: "New Subtitle", ar: "عنوان فرعي جديد" },
    description: { en: "New description", ar: "وصف جديد" },
    button: {
      text: { en: "Click Here", ar: "انقر هنا" },
      link: "/projects"
    }
  })
});

// PATCH Update Specific Section
const sectionResponse = await fetch('/api/admin/home-page/featured-projects/section/', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    section: "title",
    data: { en: "Updated Title", ar: "عنوان محدث" }
  })
});
```

---

## Error Handling

### Standard Error Codes
- **401**: Authentication required/invalid token
- **403**: Insufficient permissions (`PERMISSION_DENIED`)
- **404**: Content not found (`FEATURED_PROJECTS_NOT_FOUND`)
- **400**: Validation errors (`VALIDATION_ERROR`)
- **500**: Internal server error (`INTERNAL_ERROR`)

### Error Response Format
All error responses follow the same format:
```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE",
  "errors": {
    "field_name": ["Error message"]
  }
}
```

---

## Testing

### Test Coverage
- ✅ **GET Featured Projects**: Content retrieval
- ✅ **PUT Featured Projects**: Full content updates
- ✅ **PATCH Section Updates**: Granular section updates
- ✅ **GET History**: Revision history with pagination
- ✅ **Validation Errors**: Comprehensive error handling
- ✅ **Permission Checking**: Authentication and authorization
- ✅ **Response Structure**: Consistent API response format

### Test Script
A comprehensive test script is available: `test_featured_projects_endpoints.py`

---

## Related Endpoints
- `GET /api/admin/home-page/hero/` - Hero section (same pattern)
- `PUT /api/admin/home-page/hero/` - Hero section updates (same pattern)
- `PATCH /api/admin/home-page/hero/section/` - Hero section updates (same pattern)
- `GET /api/admin/home-page/hero/history/` - Hero section history (same pattern)

---

## Notes
- The implementation creates a new Featured Projects section if none exists
- All changes are tracked in revision history
- The response includes both the updated data and complete metadata
- Subtitle fields are optional to provide flexibility
- URL validation allows both relative paths and full URLs
- The API maintains complete compatibility with the existing Hero section frontend code 
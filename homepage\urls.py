from django.urls import path
from . import views

# Homepage URLs
urlpatterns = [
    # Hero section endpoints
    path('hero/', views.hero_content_handler, name='hero-content'),
    path('hero/section/', views.update_hero_section, name='update-hero-section'),
    path('hero/history/', views.get_hero_history, name='get-hero-history'),

    # About Us section endpoints
    path('about-us/', views.about_us_content_handler, name='about-us-content'),
    path('about-us/upload-image/', views.upload_about_us_image, name='upload-about-us-image'),
    path('about-us/image/', views.about_us_image_upload, name='about-us-image-upload'),

    # Featured Projects section endpoints
    path('featured-projects/', views.featured_projects_content_handler, name='featured-projects-content'),
    path('featured-projects/section/', views.update_featured_projects_section, name='update-featured-projects-section'),
    path('featured-projects/history/', views.get_featured_projects_history, name='get-featured-projects-history'),

    # Achievements section endpoints
    path('achievements/', views.achievements_content_handler, name='achievements-content'),
    path('achievements/history/', views.get_achievements_history, name='get-achievements-history'),

    # Achievements items endpoints
    path('achievements/items/', views.achievements_items_handler, name='achievements-items'),
    path('achievements/items/<int:item_id>/', views.achievements_item_detail_handler, name='achievements-item-detail'),

    # Latest Articles section endpoints
    path('latest-articles/', views.latest_articles_content_handler, name='latest-articles-content'),
    path('latest-articles/section/', views.update_latest_articles_section, name='update-latest-articles-section'),
    path('latest-articles/history/', views.get_latest_articles_history, name='get-latest-articles-history'),

    # Testimonials section endpoints
    path('testimonials/', views.testimonials_content_handler, name='testimonials-content'),
    path('testimonials/section/', views.update_testimonials_section, name='update-testimonials-section'),
    path('testimonials/history/', views.get_testimonials_history, name='get-testimonials-history'),

]
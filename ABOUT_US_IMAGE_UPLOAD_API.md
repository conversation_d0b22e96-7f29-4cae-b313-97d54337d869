# About Us Image Upload API Documentation

## Overview
This endpoint allows authenticated admin users to upload/update the main image for the About Us section, following the same pattern as the avatar upload functionality.

## Endpoint Details

### Upload About Us Image
**POST** `/api/admin/home-page/about-us/image/`

Upload or update the main image for the About Us section.

#### Authentication
- **Required**: Yes
- **Type**: <PERSON><PERSON> (JWT)
- **Header**: `Authorization: Bearer <access_token>`

#### Permissions
- User must have homepage management permissions
- Super Admin, Admin, or staff users with appropriate permissions

#### Request Format
- **Content-Type**: `multipart/form-data`
- **Method**: POST

#### Request Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `image` | File | Yes | Image file (JPEG, PNG, GIF) |

#### File Validation
- **Maximum Size**: 5MB
- **Allowed Formats**: JPEG, PNG, GIF
- **Content Types**: `image/jpeg`, `image/jpg`, `image/png`, `image/gif`

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Image updated successfully",
  "data": {
    "image": "/media/uploads/about-us/image_filename.jpg",
    "aboutUs": {
      "id": 1,
      "badge": {
        "en": "About Mazaya Capital",
        "ar": "حول مزايا كابيتال"
      },
      "title": {
        "en": "Welcome to Mazaya",
        "ar": "مرحبا بك في مزايا"
      },
      "subtitle": {
        "en": "Mazaya Capital",
        "ar": "مزايا كابيتال"
      },
      "description": {
        "en": "A leading real estate development company...",
        "ar": "شركة تطوير عقاري رائدة..."
      },
      "imageOverlay": {
        "badge": {
          "en": "Excellence",
          "ar": "التميز"
        },
        "text": {
          "en": "Shaping Landmarks Since 2005",
          "ar": "نشكل المعالم منذ عام ٢٠٠٥"
        }
      },
      "mainImage": "/media/uploads/about-us/image_filename.jpg",
      "mainImageFile": "/media/uploads/about-us/image_filename.jpg",
      "imageAltText": {
        "en": "Mazaya Capital office building",
        "ar": "مبنى مكاتب مزايا كابيتال"
      },
      "created_at": "2025-05-26T05:54:55.705348Z",
      "updated_at": "2025-05-29T08:00:52.111690Z",
      "updated_by": {
        "id": 2,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "User"
      }
    }
  }
}
```

#### Error Responses

##### 400 Bad Request - No File Provided
```json
{
  "success": false,
  "message": "No file provided",
  "errors": {
    "image": ["Image file is required."]
  }
}
```

##### 400 Bad Request - Invalid File Format
```json
{
  "success": false,
  "message": "Invalid file format",
  "errors": {
    "image": ["Only JPEG, PNG, and GIF files are allowed."]
  }
}
```

##### 413 Request Entity Too Large - File Too Large
```json
{
  "success": false,
  "message": "File too large",
  "errors": {
    "image": ["File size must be less than 5MB."]
  }
}
```

##### 401 Unauthorized - Invalid/Missing Token
```json
{
  "success": false,
  "message": "Authentication credentials were not provided."
}
```

##### 403 Forbidden - Insufficient Permissions
```json
{
  "success": false,
  "message": "Permission denied. You don't have access to update home page content.",
  "error_code": "PERMISSION_DENIED"
}
```

##### 404 Not Found - About Us Section Not Found
```json
{
  "success": false,
  "message": "About us section not found. Please create content first.",
  "error_code": "ABOUT_US_NOT_FOUND"
}
```

## Implementation Details

### Pattern Consistency
This endpoint follows the exact same pattern as the avatar upload endpoint (`POST /api/admin/auth/avatar/`):

1. **File Parameter**: Uses `image` instead of `avatar`
2. **Validation**: Same file size (5MB) and type restrictions
3. **Response Format**: Returns both the image URL and complete section data
4. **Error Handling**: Consistent error messages and status codes
5. **File Management**: Automatically deletes old image when uploading new one

### Features
- **Automatic File Cleanup**: Old images are automatically deleted when new ones are uploaded
- **Revision Tracking**: Changes are tracked in the revision history
- **Permission-Based Access**: Role-based access control
- **Comprehensive Validation**: File size, type, and format validation
- **Bilingual Support**: Works with existing bilingual content structure

### Frontend Integration
Since this follows the same pattern as avatar upload, you can use the same frontend approach:

```javascript
// Example frontend usage (similar to avatar upload)
const formData = new FormData();
formData.append('image', imageFile);

const response = await fetch('/api/admin/home-page/about-us/image/', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`
  },
  body: formData
});

const result = await response.json();
if (result.success) {
  console.log('Image uploaded:', result.data.image);
  console.log('Updated about us data:', result.data.aboutUs);
}
```

## Testing

### Test Cases Covered
1. ✅ **Successful Image Upload**: Valid image file upload
2. ✅ **No File Error**: Request without image file
3. ✅ **Invalid File Type**: Non-image file upload
4. ⚠️ **Large File Error**: File size validation (needs adjustment)

### Test Script
A comprehensive test script is available: `test_about_us_image_upload.py`

## Related Endpoints
- `GET /api/admin/home-page/about-us/` - Get About Us content
- `PUT/PATCH /api/admin/home-page/about-us/` - Update About Us content
- `POST /api/admin/auth/avatar/` - Upload user avatar (same pattern)

## Notes
- The endpoint requires an existing About Us section to be created first
- Images are stored in `/media/uploads/about-us/` directory
- The response includes both the direct image URL and complete About Us section data
- Revision history is automatically maintained for all changes 
# Generated by Django 5.2.1 on 2025-05-29 11:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('homepage', '0004_remove_main_image_url_make_file_required'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FeaturedProjectsSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_en', models.CharField(max_length=100, verbose_name='Title (English)')),
                ('title_ar', models.Char<PERSON>ield(max_length=100, verbose_name='Title (Arabic)')),
                ('subtitle_en', models.CharField(blank=True, max_length=150, verbose_name='Subtitle (English)')),
                ('subtitle_ar', models.Char<PERSON>ield(blank=True, max_length=150, verbose_name='Subtitle (Arabic)')),
                ('description_en', models.TextField(max_length=500, verbose_name='Description (English)')),
                ('description_ar', models.TextField(max_length=500, verbose_name='Description (Arabic)')),
                ('button_text_en', models.CharField(max_length=50, verbose_name='Button Text (English)')),
                ('button_text_ar', models.CharField(max_length=50, verbose_name='Button Text (Arabic)')),
                ('button_link', models.CharField(max_length=200, verbose_name='Button Link')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='featured_projects_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Featured Projects Section',
                'verbose_name_plural': 'Featured Projects Sections',
                'db_table': 'homepage_featured_projects_section',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='FeaturedProjectsSectionRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('changes', models.JSONField(verbose_name='Changes Made')),
                ('updated_at', models.DateTimeField(auto_now_add=True)),
                ('featured_projects_section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions', to='homepage.featuredprojectssection')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Featured Projects Section Revision',
                'verbose_name_plural': 'Featured Projects Section Revisions',
                'db_table': 'homepage_featured_projects_revisions',
                'ordering': ['-updated_at'],
            },
        ),
    ]

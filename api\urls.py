from django.urls import path, include
from . import views

# Admin auth URLs
admin_auth_patterns = [
    path('login/', views.admin_login, name='admin-login'),
    path('logout/', views.admin_logout, name='admin-logout'),
    path('refresh/', views.admin_refresh_token, name='admin-refresh'),
    path('password-reset/', views.admin_password_reset, name='admin-password-reset'),
    path('me/', views.admin_profile, name='admin-profile'),
    path('profile/', views.admin_profile_update, name='admin-profile-update'),
    path('avatar/', views.admin_avatar_upload, name='admin-avatar-upload'),
    path('avatar/', views.admin_avatar_delete, name='admin-avatar-delete'),
]

urlpatterns = [
    path('', views.api_overview, name="api-overview"),
    path('admin/auth/', include(admin_auth_patterns)),
] 
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from homepage.models import FeaturedProjectsSection

User = get_user_model()


class Command(BaseCommand):
    help = 'Initialize Featured Projects section with sample content'

    def handle(self, *args, **options):
        # Check if Featured Projects section already exists
        if FeaturedProjectsSection.objects.filter(is_active=True).exists():
            self.stdout.write(
                self.style.WARNING('Featured Projects section already exists. Skipping initialization.')
            )
            return

        # Get or create a default admin user for the initial content
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.filter(role='Super Admin').first()
        if not admin_user:
            admin_user = User.objects.filter(email='<EMAIL>').first()

        # Create Featured Projects section with sample content
        featured_projects_section = FeaturedProjectsSection.objects.create(
            title_en="Featured Projects",
            title_ar="المشاريع المميزة",
            subtitle_en="Exceptional Developments",
            subtitle_ar="تطويرات استثنائية",
            description_en="Discover our exceptional real estate developments with premium locations and world-class amenities",
            description_ar="اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى",
            button_text_en="View All Projects",
            button_text_ar="عرض جميع المشاريع",
            button_link="/projects",
            updated_by=admin_user,
            is_active=True
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created Featured Projects section with ID: {featured_projects_section.id}'
            )
        )
        
        self.stdout.write(
            self.style.SUCCESS('Featured Projects section initialization completed!')
        ) 
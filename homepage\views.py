from django.shortcuts import render
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
import copy
import os
import uuid
from django.conf import settings
from PIL import Image
import json

from .models import (
    AchievementsSection, AchievementsSectionRevision, AchievementsItem, AchievementsItemRevision,
    HeroSection, HeroSectionRevision, AboutUsSection, AboutUsSectionRevision,
    FeaturedProjectsSection, FeaturedProjectsSectionRevision, LatestArticlesSection, LatestArticlesSectionRevision, TestimonialsSection, TestimonialsSectionRevision
)
from .serializers import (
    AchievementsSectionSerializer, AchievementsSectionUpdateSerializer, AchievementsSectionRevisionSerializer,
    AchievementsItemSerializer, AchievementsItemUpdateSerializer, AchievementsItemRevisionSerializer,
    AchievementsItemSection<PERSON><PERSON>dateSerializer,
    HeroSectionSerializer,
    HeroSec<PERSON>UpdateSerializer,
    HeroSectionSectionUpdateSerializer,
    HeroSectionRevisionSerializer,
    AboutUsSectionSerializer,
    AboutUsSectionUpdateSerializer,
    AboutUsSectionRevisionSerializer,
    AboutUsImageUploadSerializer,
    FeaturedProjectsSectionSerializer,
    FeaturedProjectsSectionUpdateSerializer,
    FeaturedProjectsSectionSectionUpdateSerializer,
    FeaturedProjectsSectionRevisionSerializer,
    LatestArticlesSectionSerializer,
    LatestArticlesSectionUpdateSerializer,
    LatestArticlesSectionSectionUpdateSerializer,
    LatestArticlesSectionRevisionSerializer, TestimonialsSectionRevisionSerializer, TestimonialsSectionSectionUpdateSerializer, TestimonialsSectionSerializer, TestimonialsSectionUpdateSerializer
)


def check_homepage_permission(user):
    """
    Check if user has permission to manage home page content
    """
    # For Super Admin, allow access
    if hasattr(user, 'role') and user.role == 'Super Admin':
        return True
    
    # For staff users, allow access
    if user.is_staff or user.is_superuser:
        return True
    
    # Check if user has the required permissions
    if hasattr(user, 'permissions'):
        user_permissions = user.permissions
        # Updated to use permissions that actually exist in the user model
        required_permissions = ['edit_content', 'manage_users', 'view_dashboard']
        return any(perm in user_permissions for perm in required_permissions)
    
    return False


@api_view(['GET', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def hero_content_handler(request):
    """
    Combined handler for hero content operations
    GET /api/admin/home-page/hero/ - Fetch current hero section content
    PUT/PATCH /api/admin/home-page/hero/ - Update hero section content
    """
    if request.method == 'GET':
        return get_hero_content(request)
    elif request.method in ['PUT', 'PATCH']:
        return update_hero_content(request)


def get_hero_content(request):
    """
    GET /api/admin/home-page/hero/
    Fetch current hero section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to manage home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get the active hero section (latest one)
        hero_section = HeroSection.objects.filter(is_active=True).first()
        
        if not hero_section:
            return Response({
                'success': False,
                'message': 'Hero content not found',
                'error_code': 'HERO_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = HeroSectionSerializer(hero_section)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching hero content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_hero_content(request):
    """
    PUT/PATCH /api/admin/home-page/hero/
    Update hero section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get or create hero section
        hero_section = HeroSection.objects.filter(is_active=True).first()
        
        if not hero_section:
            # Create new hero section if none exists
            hero_section = HeroSection(is_active=True)
        
        # Store old data for revision tracking
        old_data = copy.deepcopy(hero_section.structured_data) if hero_section.id else {}
        
        # Validate and update
        serializer = HeroSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            with transaction.atomic():
                # Update the hero section
                hero_section.updated_by = request.user
                updated_hero = serializer.update(hero_section, serializer.validated_data)
                
                # Create revision record
                if old_data:
                    changes = {}
                    new_data = updated_hero.structured_data
                    
                    # Track changes
                    for key in ['badge', 'title', 'description', 'buttons', 'bottomText']:
                        if old_data.get(key) != new_data.get(key):
                            changes[key] = {
                                'old': old_data.get(key),
                                'new': new_data.get(key)
                            }
                    
                    if changes:
                        HeroSectionRevision.objects.create(
                            hero_section=updated_hero,
                            changes=changes,
                            updated_by=request.user
                        )
                
                # Return updated data
                response_serializer = HeroSectionSerializer(updated_hero)
                
                return Response({
                    'success': True,
                    'message': 'Hero section content updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating hero content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_hero_section(request):
    """
    PATCH /api/admin/home-page/hero/section/
    Update specific sections of hero content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get hero section
        hero_section = HeroSection.objects.filter(is_active=True).first()
        
        if not hero_section:
            return Response({
                'success': False,
                'message': 'Hero content not found',
                'error_code': 'HERO_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate section update
        serializer = HeroSectionSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            section = serializer.validated_data['section']
            data = serializer.validated_data['data']
            
            # Store old data for revision
            old_section_data = getattr(hero_section, f'get_{section}', lambda: {})()
            
            # Update specific section
            with transaction.atomic():
                if section == 'badge':
                    hero_section.badge_en = data['en']
                    hero_section.badge_ar = data['ar']
                elif section == 'title':
                    hero_section.title_line1_en = data['line1']['en']
                    hero_section.title_line1_ar = data['line1']['ar']
                    hero_section.title_line2_en = data['line2']['en']
                    hero_section.title_line2_ar = data['line2']['ar']
                    hero_section.title_line3_en = data['line3']['en']
                    hero_section.title_line3_ar = data['line3']['ar']
                elif section == 'description':
                    hero_section.description_en = data['en']
                    hero_section.description_ar = data['ar']
                elif section == 'bottomText':
                    hero_section.bottom_text_en = data['en']
                    hero_section.bottom_text_ar = data['ar']
                
                hero_section.updated_by = request.user
                hero_section.save()
                
                # Create revision record
                changes = {
                    section: {
                        'old': old_section_data,
                        'new': data
                    }
                }
                
                HeroSectionRevision.objects.create(
                    hero_section=hero_section,
                    changes=changes,
                    updated_by=request.user
                )
            
            return Response({
                'success': True,
                'message': f'{section.title()} section updated successfully',
                'data': {
                    'section': section,
                    'updated_data': data,
                    'updated_at': hero_section.updated_at.isoformat()
                }
            }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating section',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class HeroRevisionPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'per_page'
    max_page_size = 50


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_hero_history(request):
    """
    GET /api/admin/home-page/hero/history/
    Get revision history for hero section
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to view home page content history.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get hero section
        hero_section = HeroSection.objects.filter(is_active=True).first()
        
        if not hero_section:
            return Response({
                'success': False,
                'message': 'Hero content not found',
                'error_code': 'HERO_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get revisions
        revisions = HeroSectionRevision.objects.filter(hero_section=hero_section)
        
        # Paginate results
        paginator = HeroRevisionPagination()
        paginated_revisions = paginator.paginate_queryset(revisions, request)
        
        serializer = HeroSectionRevisionSerializer(paginated_revisions, many=True)
        
        return Response({
            'success': True,
            'data': {
                'total': revisions.count(),
                'page': int(request.GET.get('page', 1)),
                'per_page': paginator.page_size,
                'revisions': serializer.data
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching history',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_hero_content_en(request):
    """
    GET /api/home-page/hero/en/
    Fetch current hero section content in English (Public endpoint - no authentication required)
    """
    try:
        # Get the active hero section (latest one)
        hero_section = HeroSection.objects.filter(is_active=True).first()
        
        if not hero_section:
            return Response({
                'success': False,
                'message': 'Hero content not found',
                'error_code': 'HERO_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return English content only
        data = {
            'id': hero_section.id,
            'badge': hero_section.badge_en,
            'title': {
                'line1': hero_section.title_line1_en,
                'line2': hero_section.title_line2_en,
                'line3': hero_section.title_line3_en
            },
            'description': hero_section.description_en,
            'buttons': {
                'primary': {
                    'text': hero_section.primary_button_text_en,
                    'link': hero_section.primary_button_link
                },
                'secondary': {
                    'text': hero_section.secondary_button_text_en,
                    'link': hero_section.secondary_button_link
                }
            },
            'bottomText': hero_section.bottom_text_en,
            'created_at': hero_section.created_at.isoformat(),
            'updated_at': hero_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching hero content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_hero_content_ar(request):
    """
    GET /api/home-page/hero/ar/
    Fetch current hero section content in Arabic (Public endpoint - no authentication required)
    """
    try:
        # Get the active hero section (latest one)
        hero_section = HeroSection.objects.filter(is_active=True).first()
        
        if not hero_section:
            return Response({
                'success': False,
                'message': 'Hero content not found',
                'error_code': 'HERO_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return Arabic content only
        data = {
            'id': hero_section.id,
            'badge': hero_section.badge_ar,
            'title': {
                'line1': hero_section.title_line1_ar,
                'line2': hero_section.title_line2_ar,
                'line3': hero_section.title_line3_ar
            },
            'description': hero_section.description_ar,
            'buttons': {
                'primary': {
                    'text': hero_section.primary_button_text_ar,
                    'link': hero_section.primary_button_link
                },
                'secondary': {
                    'text': hero_section.secondary_button_text_ar,
                    'link': hero_section.secondary_button_link
                }
            },
            'bottomText': hero_section.bottom_text_ar,
            'created_at': hero_section.created_at.isoformat(),
            'updated_at': hero_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching hero content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ============================================================================
# ABOUT US SECTION VIEWS
# ============================================================================

@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_about_us_content_en(request):
    """
    GET /api/home-page/about-us/en/
    Fetch current About Us section content in English (Public endpoint - no authentication required)
    """
    try:
        # Get the active About Us section (latest one)
        about_us_section = AboutUsSection.objects.filter(is_active=True).first()
        
        if not about_us_section:
            return Response({
                'success': False,
                'message': 'About us content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return English content only
        data = {
            'id': about_us_section.id,
            'badge': about_us_section.badge_en,
            'title': about_us_section.title_en,
            'subtitle': about_us_section.subtitle_en,
            'description': about_us_section.description_en,
            'imageOverlay': {
                'badge': about_us_section.image_overlay_badge_en,
                'text': about_us_section.image_overlay_text_en
            },
            'mainImage': about_us_section.main_image,
            'created_at': about_us_section.created_at.isoformat(),
            'updated_at': about_us_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'About us content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching about us content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_about_us_content_ar(request):
    """
    GET /api/home-page/about-us/ar/
    Fetch current About Us section content in Arabic (Public endpoint - no authentication required)
    """
    try:
        # Get the active About Us section (latest one)
        about_us_section = AboutUsSection.objects.filter(is_active=True).first()
        
        if not about_us_section:
            return Response({
                'success': False,
                'message': 'About us content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return Arabic content only
        data = {
            'id': about_us_section.id,
            'badge': about_us_section.badge_ar,
            'title': about_us_section.title_ar,
            'subtitle': about_us_section.subtitle_ar,
            'description': about_us_section.description_ar,
            'imageOverlay': {
                'badge': about_us_section.image_overlay_badge_ar,
                'text': about_us_section.image_overlay_text_ar
            },
            'mainImage': about_us_section.main_image,
            'created_at': about_us_section.created_at.isoformat(),
            'updated_at': about_us_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'About us content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching about us content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def about_us_content_handler(request):
    """
    Combined handler for About Us content operations
    GET /api/admin/home-page/about-us/ - Fetch current About Us section content
    PUT/PATCH /api/admin/home-page/about-us/ - Update About Us section content
    """
    if request.method == 'GET':
        return get_about_us_admin_content(request)
    elif request.method in ['PUT', 'PATCH']:
        return update_about_us_content(request)


def get_about_us_admin_content(request):
    """
    GET /api/admin/home-page/about-us/
    Fetch current About Us section content for admin
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to manage home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get the active About Us section (latest one)
        about_us_section = AboutUsSection.objects.filter(is_active=True).first()
        
        if not about_us_section:
            return Response({
                'success': False,
                'message': 'About us content not found',
                'error_code': 'ABOUT_US_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = AboutUsSectionSerializer(about_us_section)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching about us content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_about_us_content(request):
    """
    PUT/PATCH /api/admin/home-page/about-us/
    Update About Us section content (now supports both JSON and multipart data for file uploads)
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get or create About Us section
        about_us_section = AboutUsSection.objects.filter(is_active=True).first()
        
        if not about_us_section:
            # Create new About Us section if none exists
            about_us_section = AboutUsSection(is_active=True)
        
        # Store old data for revision tracking
        old_data = copy.deepcopy(about_us_section.structured_data) if about_us_section.id else {}
        
        # Validate and update
        serializer = AboutUsSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            with transaction.atomic():
                # Update the About Us section
                about_us_section.updated_by = request.user
                updated_about_us = serializer.update(about_us_section, serializer.validated_data)
                
                # Create revision record
                if old_data:
                    changes = {}
                    new_data = updated_about_us.structured_data
                    
                    # Track changes
                    for key in ['badge', 'title', 'subtitle', 'description', 'imageOverlay', 'mainImage', 'mainImageFile']:
                        if old_data.get(key) != new_data.get(key):
                            changes[key] = {
                                'old': old_data.get(key),
                                'new': new_data.get(key)
                            }
                    
                    if changes:
                        AboutUsSectionRevision.objects.create(
                            about_us_section=updated_about_us,
                            changes=changes,
                            updated_by=request.user
                        )
                
                # Return updated data
                response_serializer = AboutUsSectionSerializer(updated_about_us)
                
                return Response({
                    'success': True,
                    'message': 'About us content updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating about us content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_about_us_image(request):
    """
    POST /api/admin/home-page/about-us/upload-image/
    Upload a new main image for the About Us section
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to upload images.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get or create About Us section
        about_us_section = AboutUsSection.objects.filter(is_active=True).first()
        if not about_us_section:
            return Response({
                'success': False,
                'message': 'About us section not found. Please create content first.',
                'error_code': 'ABOUT_US_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check if image file is provided
        if 'image' not in request.FILES:
            return Response({
                'success': False,
                'message': 'No image file provided',
                'error_code': 'NO_IMAGE_FILE'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        uploaded_file = request.FILES['image']
        
        # Validate file size (5MB limit)
        if uploaded_file.size > 5 * 1024 * 1024:
            return Response({
                'success': False,
                'message': 'File size exceeds maximum limit of 5MB',
                'error_code': 'FILE_TOO_LARGE'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate file type
        allowed_types = ['image/jpeg', 'image/png', 'image/webp']
        if uploaded_file.content_type not in allowed_types:
            return Response({
                'success': False,
                'message': 'File type not supported. Please upload JPEG, PNG, or WebP images.',
                'error_code': 'INVALID_FILE_TYPE'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Store old data for revision tracking
        old_data = copy.deepcopy(about_us_section.structured_data)
        
        with transaction.atomic():
            # Update the About Us section with new image
            about_us_section.main_image_file = uploaded_file
            about_us_section.updated_by = request.user
            
            # Update alt text if provided
            try:
                if 'imageAltText' in request.data:
                    alt_text_data = json.loads(request.data['imageAltText']) if isinstance(request.data['imageAltText'], str) else request.data['imageAltText']
                    alt_text_en = alt_text_data.get('en', '')
                    alt_text_ar = alt_text_data.get('ar', '')
                    if alt_text_en:
                        about_us_section.image_alt_text_en = alt_text_en
                    if alt_text_ar:
                        about_us_section.image_alt_text_ar = alt_text_ar
            except (json.JSONDecodeError, TypeError):
                # If alt text parsing fails, continue without it
                pass
            
            about_us_section.save()
            
            # Create revision record
            new_data = about_us_section.structured_data
            changes = {}
            
            # Track changes
            for key in ['mainImage', 'mainImageFile', 'imageAltText']:
                if old_data.get(key) != new_data.get(key):
                    changes[key] = {
                        'old': old_data.get(key),
                        'new': new_data.get(key)
                    }
            
            if changes:
                AboutUsSectionRevision.objects.create(
                    about_us_section=about_us_section,
                    changes=changes,
                    updated_by=request.user
                )
        
        # Get image dimensions
        try:
            with Image.open(about_us_section.main_image_file.path) as img:
                width, height = img.size
        except Exception:
            width, height = None, None
        
        return Response({
            'success': True,
            'message': 'Image uploaded successfully',
            'data': {
                'mainImage': about_us_section.main_image,
                'mainImageFile': about_us_section.main_image_file.url,
                'imageAltText': {
                    'en': about_us_section.image_alt_text_en,
                    'ar': about_us_section.image_alt_text_ar
                },
                'fileSize': uploaded_file.size,
                'dimensions': {
                    'width': width,
                    'height': height
                },
                'uploadedAt': timezone.now().isoformat()
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred during upload',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def about_us_image_upload(request):
    """
    Upload/Update About Us section image (following avatar upload pattern)
    POST /api/admin/home-page/about-us/image/
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # Get or create About Us section
    about_us_section = AboutUsSection.objects.filter(is_active=True).first()
    if not about_us_section:
        return Response({
            'success': False,
            'message': 'About us section not found. Please create content first.',
            'error_code': 'ABOUT_US_NOT_FOUND'
        }, status=status.HTTP_404_NOT_FOUND)
    
    # Check if image file is provided
    if 'image' not in request.FILES:
        return Response({
            'success': False,
            'message': 'No file provided',
            'errors': {
                'image': ['Image file is required.']
            }
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Check file size before processing (5MB limit)
    image_file = request.FILES['image']
    if image_file.size > 5 * 1024 * 1024:
        return Response({
            'success': False,
            'message': 'File too large',
            'errors': {
                'image': ['File size must be less than 5MB.']
            }
        }, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)
    
    serializer = AboutUsImageUploadSerializer(about_us_section, data=request.data, partial=True)
    
    if serializer.is_valid():
        # Store old data for revision tracking
        old_data = copy.deepcopy(about_us_section.structured_data)
        
        updated_section = serializer.save()
        updated_section.updated_by = request.user
        updated_section.save()
        
        # Get the image URL
        image_url = updated_section.main_image_file.url if updated_section.main_image_file else '/images/home/<USER>'
        
        # Serialize complete about us data
        section_serializer = AboutUsSectionSerializer(updated_section)
        
        # Create revision record
        new_data = updated_section.structured_data
        changes = {}
        
        # Track changes
        for key in ['mainImage', 'mainImageFile']:
            if old_data.get(key) != new_data.get(key):
                changes[key] = {
                    'old': old_data.get(key),
                    'new': new_data.get(key)
                }
        
        if changes:
            AboutUsSectionRevision.objects.create(
                about_us_section=updated_section,
                changes=changes,
                updated_by=request.user
            )
        
        return Response({
            'success': True,
            'message': 'Image updated successfully',
            'data': {
                'image': image_url,
                'aboutUs': section_serializer.data
            }
        }, status=status.HTTP_200_OK)
    
    # Handle validation errors
    errors = serializer.errors
    if 'image' in errors:
        error_message = errors['image'][0]
        if 'Only JPEG, PNG, and GIF files are allowed.' in error_message:
            return Response({
                'success': False,
                'message': 'Invalid file format',
                'errors': {
                    'image': ['Only JPEG, PNG, and GIF files are allowed.']
                }
            }, status=status.HTTP_400_BAD_REQUEST)
        elif 'File size must be less than 5MB.' in error_message:
            return Response({
                'success': False,
                'message': 'File too large',
                'errors': {
                    'image': ['File size must be less than 5MB.']
                }
            }, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)
    
    return Response({
        'success': False,
        'message': 'Invalid image file',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


# ============================================================================
# FEATURED PROJECTS SECTION VIEWS
# ============================================================================

@api_view(['GET', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def featured_projects_content_handler(request):
    """
    Combined handler for featured projects content operations
    GET /api/admin/home-page/featured-projects/ - Fetch current featured projects section content
    PUT/PATCH /api/admin/home-page/featured-projects/ - Update featured projects section content
    """
    if request.method == 'GET':
        return get_featured_projects_content(request)
    elif request.method in ['PUT', 'PATCH']:
        return update_featured_projects_content(request)


def get_featured_projects_content(request):
    """
    GET /api/admin/home-page/featured-projects/
    Fetch current featured projects section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to manage home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get the active featured projects section (latest one)
        featured_projects_section = FeaturedProjectsSection.objects.filter(is_active=True).first()
        
        if not featured_projects_section:
            return Response({
                'success': False,
                'message': 'Featured projects content not found',
                'error_code': 'FEATURED_PROJECTS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = FeaturedProjectsSectionSerializer(featured_projects_section)
        
        return Response({
            'success': True,
            'message': 'Featured projects content retrieved successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching featured projects content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_featured_projects_content(request):
    """
    PUT/PATCH /api/admin/home-page/featured-projects/
    Update featured projects section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get or create featured projects section
        featured_projects_section = FeaturedProjectsSection.objects.filter(is_active=True).first()
        
        if not featured_projects_section:
            # Create new featured projects section if none exists
            featured_projects_section = FeaturedProjectsSection(is_active=True)
        
        # Store old data for revision tracking
        old_data = copy.deepcopy(featured_projects_section.structured_data) if featured_projects_section.id else {}
        
        # Validate and update
        serializer = FeaturedProjectsSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            with transaction.atomic():
                # Update the featured projects section
                featured_projects_section.updated_by = request.user
                updated_section = serializer.update(featured_projects_section, serializer.validated_data)
                
                # Create revision record
                if old_data:
                    changes = {}
                    new_data = updated_section.structured_data
                    
                    # Track changes
                    for key in ['title', 'subtitle', 'description', 'button']:
                        if old_data.get(key) != new_data.get(key):
                            changes[key] = {
                                'old': old_data.get(key),
                                'new': new_data.get(key)
                            }
                    
                    if changes:
                        FeaturedProjectsSectionRevision.objects.create(
                            featured_projects_section=updated_section,
                            changes=changes,
                            updated_by=request.user
                        )
                
                # Return updated data
                response_serializer = FeaturedProjectsSectionSerializer(updated_section)
                
                return Response({
                    'success': True,
                    'message': 'Featured projects section updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating featured projects content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_featured_projects_section(request):
    """
    PATCH /api/admin/home-page/featured-projects/section/
    Update specific sections of featured projects content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get featured projects section
        featured_projects_section = FeaturedProjectsSection.objects.filter(is_active=True).first()
        
        if not featured_projects_section:
            return Response({
                'success': False,
                'message': 'Featured projects content not found',
                'error_code': 'FEATURED_PROJECTS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate section update
        serializer = FeaturedProjectsSectionSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            section = serializer.validated_data['section']
            data = serializer.validated_data['data']
            
            # Store old data for revision
            old_section_data = getattr(featured_projects_section.structured_data, section, {})
            
            # Update specific section
            with transaction.atomic():
                if section == 'title':
                    featured_projects_section.title_en = data['en']
                    featured_projects_section.title_ar = data['ar']
                elif section == 'subtitle':
                    featured_projects_section.subtitle_en = data.get('en', featured_projects_section.subtitle_en)
                    featured_projects_section.subtitle_ar = data.get('ar', featured_projects_section.subtitle_ar)
                elif section == 'description':
                    featured_projects_section.description_en = data['en']
                    featured_projects_section.description_ar = data['ar']
                elif section == 'button':
                    featured_projects_section.button_text_en = data['text']['en']
                    featured_projects_section.button_text_ar = data['text']['ar']
                    featured_projects_section.button_link = data['link']
                
                featured_projects_section.updated_by = request.user
                featured_projects_section.save()
                
                # Create revision record
                changes = {
                    section: {
                        'old': old_section_data,
                        'new': data
                    }
                }
                
                FeaturedProjectsSectionRevision.objects.create(
                    featured_projects_section=featured_projects_section,
                    changes=changes,
                    updated_by=request.user
                )
            
            return Response({
                'success': True,
                'message': f'{section.title()} section updated successfully',
                'data': {
                    'section': section,
                    'updated_data': data,
                    'updated_at': featured_projects_section.updated_at.isoformat()
                }
            }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating section',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FeaturedProjectsRevisionPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'per_page'
    max_page_size = 50


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_featured_projects_history(request):
    """
    GET /api/admin/home-page/featured-projects/history/
    Get revision history for featured projects section
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to view home page content history.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get featured projects section
        featured_projects_section = FeaturedProjectsSection.objects.filter(is_active=True).first()
        
        if not featured_projects_section:
            return Response({
                'success': False,
                'message': 'Featured projects content not found',
                'error_code': 'FEATURED_PROJECTS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get revisions
        revisions = FeaturedProjectsSectionRevision.objects.filter(featured_projects_section=featured_projects_section)
        
        # Paginate results
        paginator = FeaturedProjectsRevisionPagination()
        paginated_revisions = paginator.paginate_queryset(revisions, request)
        
        serializer = FeaturedProjectsSectionRevisionSerializer(paginated_revisions, many=True)
        
        return Response({
            'success': True,
            'data': {
                'total': revisions.count(),
                'page': int(request.GET.get('page', 1)),
                'per_page': paginator.page_size,
                'revisions': serializer.data
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching history',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_featured_projects_content_en(request):
    """
    GET /api/home-page/featured-projects/en/
    Fetch current Featured Projects section content in English (Public endpoint - no authentication required)
    """
    try:
        # Get the active Featured Projects section (latest one)
        featured_projects_section = FeaturedProjectsSection.objects.filter(is_active=True).first()
        
        if not featured_projects_section:
            return Response({
                'success': False,
                'message': 'Featured projects content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return English content only
        data = {
            'id': featured_projects_section.id,
            'title': featured_projects_section.title_en,
            'description': featured_projects_section.description_en,
            'button': {
                'text': featured_projects_section.button_text_en,
                'link': featured_projects_section.button_link
            },
            'created_at': featured_projects_section.created_at.isoformat(),
            'updated_at': featured_projects_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'Featured projects content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching featured projects content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_featured_projects_content_ar(request):
    """
    GET /api/home-page/featured-projects/ar/
    Fetch current Featured Projects section content in Arabic (Public endpoint - no authentication required)
    """
    try:
        # Get the active Featured Projects section (latest one)
        featured_projects_section = FeaturedProjectsSection.objects.filter(is_active=True).first()
        
        if not featured_projects_section:
            return Response({
                'success': False,
                'message': 'Featured projects content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return Arabic content only
        data = {
            'id': featured_projects_section.id,
            'title': featured_projects_section.title_ar,
            'description': featured_projects_section.description_ar,
            'button': {
                'text': featured_projects_section.button_text_ar,
                'link': featured_projects_section.button_link
            },
            'created_at': featured_projects_section.created_at.isoformat(),
            'updated_at': featured_projects_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'Featured projects content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching featured projects content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



# ============================================================================
# ACHIEVEMENTS SECTION VIEWS
# ============================================================================

@api_view(['GET', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def achievements_content_handler(request):
    """
    Combined handler for achievements content operations
    GET /api/admin/home-page/achievements/ - Fetch current achievements section content
    PUT/PATCH /api/admin/home-page/achievements/ - Update achievements section content
    """
    if request.method == 'GET':
        return get_achievements_content(request)
    elif request.method in ['PUT', 'PATCH']:
        return update_achievements_content(request)


def get_achievements_content(request):
    """
    GET /api/admin/home-page/achievements/
    Fetch current achievements section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to manage home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get the active achievements section (latest one)
        achievements_section = AchievementsSection.objects.filter(is_active=True).first()

        if not achievements_section:
            return Response({
                'success': False,
                'message': 'Achievements content not found',
                'error_code': 'ACHIEVEMENTS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)

        serializer = AchievementsSectionSerializer(achievements_section)

        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching achievements content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_achievements_content(request):
    """
    PUT/PATCH /api/admin/home-page/achievements/
    Update achievements section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get or create achievements section
        achievements_section = AchievementsSection.objects.filter(is_active=True).first()

        if not achievements_section:
            # Create new achievements section if none exists
            achievements_section = AchievementsSection(is_active=True)

        # Store old data for revision tracking
        old_data = copy.deepcopy(achievements_section.structured_data) if achievements_section.id else {}

        # Validate and update
        serializer = AchievementsSectionUpdateSerializer(data=request.data)

        if serializer.is_valid():
            with transaction.atomic():
                # Update the achievements section
                achievements_section.updated_by = request.user
                updated_achievements = serializer.update(achievements_section, serializer.validated_data)

                # Create revision record
                if old_data:
                    changes = {}
                    new_data = updated_achievements.structured_data

                    # Track changes
                    for key in ['badge', 'title', 'supporting_text']:
                        if old_data.get(key) != new_data.get(key):
                            changes[key] = {
                                'old': old_data.get(key),
                                'new': new_data.get(key)
                            }

                    if changes:
                        AchievementsSectionRevision.objects.create(
                            achievements_section=updated_achievements,
                            changes=changes,
                            updated_by=request.user
                        )

                # Return updated data
                response_serializer = AchievementsSectionSerializer(updated_achievements)

                return Response({
                    'success': True,
                    'message': 'Achievements section content updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)

        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating achievements content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AchievementsRevisionPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'per_page'
    max_page_size = 50


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_achievements_history(request):
    """
    GET /api/admin/home-page/achievements/history/
    Get revision history for achievements section
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to view home page content history.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get achievements section
        achievements_section = AchievementsSection.objects.filter(is_active=True).first()

        if not achievements_section:
            return Response({
                'success': False,
                'message': 'Achievements content not found',
                'error_code': 'ACHIEVEMENTS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)

        # Get revisions
        revisions = AchievementsSectionRevision.objects.filter(achievements_section=achievements_section)

        # Paginate results
        paginator = AchievementsRevisionPagination()
        paginated_revisions = paginator.paginate_queryset(revisions, request)

        serializer = AchievementsSectionRevisionSerializer(paginated_revisions, many=True)

        return Response({
            'success': True,
            'data': {
                'total': revisions.count(),
                'page': int(request.GET.get('page', 1)),
                'per_page': paginator.page_size,
                'revisions': serializer.data
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching history',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_achievements_content_en(request):
    """
    GET /api/home-page/achievements/en/
    Fetch current Achievements section content in English (Public endpoint - no authentication required)
    """
    try:
        # Get the active Achievements section (latest one)
        achievements_section = AchievementsSection.objects.filter(is_active=True).first()

        if not achievements_section:
            return Response({
                'success': False,
                'message': 'Achievements content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)

        # Return English content only
        data = {
            'id': achievements_section.id,
            'badge': achievements_section.badge_en,
            'title': achievements_section.title_en,
            'supporting_text': achievements_section.supporting_text_en,
            'created_at': achievements_section.created_at.isoformat(),
            'updated_at': achievements_section.updated_at.isoformat()
        }

        return Response({
            'success': True,
            'message': 'Achievements content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching achievements content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_achievements_content_ar(request):
    """
    GET /api/home-page/achievements/ar/
    Fetch current Achievements section content in Arabic (Public endpoint - no authentication required)
    """
    try:
        # Get the active Achievements section (latest one)
        achievements_section = AchievementsSection.objects.filter(is_active=True).first()

        if not achievements_section:
            return Response({
                'success': False,
                'message': 'Achievements content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)

        # Return Arabic content only
        data = {
            'id': achievements_section.id,
            'badge': achievements_section.badge_ar,
            'title': achievements_section.title_ar,
            'supporting_text': achievements_section.supporting_text_ar,
            'created_at': achievements_section.created_at.isoformat(),
            'updated_at': achievements_section.updated_at.isoformat()
        }

        return Response({
            'success': True,
            'message': 'Achievements content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching achievements content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ============================================================================
# ACHIEVEMENTS ITEMS VIEWS
# ============================================================================

class AchievementsItemPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'per_page'
    max_page_size = 100


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def achievements_items_handler(request):
    """
    Combined handler for achievements items operations
    GET /api/admin/home-page/achievements/items/ - List all achievement items
    POST /api/admin/home-page/achievements/items/ - Create new achievement item
    """
    if request.method == 'GET':
        return list_achievements_items(request)
    elif request.method == 'POST':
        return create_achievements_item(request)


def list_achievements_items(request):
    """
    GET /api/admin/home-page/achievements/items/
    List all achievement items with pagination
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to manage home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get all active achievement items
        items = AchievementsItem.objects.filter(is_active=True)

        # Paginate results
        paginator = AchievementsItemPagination()
        paginated_items = paginator.paginate_queryset(items, request)

        serializer = AchievementsItemSerializer(paginated_items, many=True)

        return Response({
            'success': True,
            'data': {
                'total': items.count(),
                'page': int(request.GET.get('page', 1)),
                'per_page': paginator.page_size,
                'items': serializer.data
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching achievement items',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def create_achievements_item(request):
    """
    POST /api/admin/home-page/achievements/items/
    Create new achievement item
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to create achievement items.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Validate and create
        serializer = AchievementsItemUpdateSerializer(data=request.data)

        if serializer.is_valid():
            with transaction.atomic():
                # Create new achievement item
                achievement_item = AchievementsItem(
                    icon_name=serializer.validated_data['icon_name'],
                    achievement_value=serializer.validated_data['achievement_value'],
                    title_en=serializer.validated_data['title']['en'],
                    title_ar=serializer.validated_data['title']['ar'],
                    updated_by=request.user,
                    is_active=True
                )
                achievement_item.save()

                # Create revision record
                AchievementsItemRevision.objects.create(
                    achievements_item=achievement_item,
                    changes={'action': 'created', 'data': achievement_item.structured_data},
                    updated_by=request.user
                )

                # Return created data
                response_serializer = AchievementsItemSerializer(achievement_item)

                return Response({
                    'success': True,
                    'message': 'Achievement item created successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_201_CREATED)

        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while creating achievement item',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def achievements_item_detail_handler(request, item_id):
    """
    Combined handler for individual achievement item operations
    GET /api/admin/home-page/achievements/items/{id}/ - Get specific achievement item
    PUT /api/admin/home-page/achievements/items/{id}/ - Update specific achievement item
    DELETE /api/admin/home-page/achievements/items/{id}/ - Delete specific achievement item
    """
    if request.method == 'GET':
        return get_achievements_item(request, item_id)
    elif request.method == 'PUT':
        return update_achievements_item(request, item_id)
    elif request.method == 'DELETE':
        return delete_achievements_item(request, item_id)


def get_achievements_item(request, item_id):
    """
    GET /api/admin/home-page/achievements/items/{id}/
    Get specific achievement item
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to view achievement items.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get the achievement item
        achievement_item = AchievementsItem.objects.filter(id=item_id, is_active=True).first()

        if not achievement_item:
            return Response({
                'success': False,
                'message': 'Achievement item not found',
                'error_code': 'ACHIEVEMENT_ITEM_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)

        serializer = AchievementsItemSerializer(achievement_item)

        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching achievement item',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_achievements_item(request, item_id):
    """
    PUT /api/admin/home-page/achievements/items/{id}/
    Update specific achievement item
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update achievement items.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get the achievement item
        achievement_item = AchievementsItem.objects.filter(id=item_id, is_active=True).first()

        if not achievement_item:
            return Response({
                'success': False,
                'message': 'Achievement item not found',
                'error_code': 'ACHIEVEMENT_ITEM_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)

        # Store old data for revision tracking
        old_data = copy.deepcopy(achievement_item.structured_data)

        # Validate and update
        serializer = AchievementsItemUpdateSerializer(data=request.data)

        if serializer.is_valid():
            with transaction.atomic():
                # Update the achievement item
                achievement_item.updated_by = request.user
                updated_item = serializer.update(achievement_item, serializer.validated_data)

                # Create revision record
                changes = {}
                new_data = updated_item.structured_data

                # Track changes
                for key in ['icon_name', 'achievement_value', 'title']:
                    if old_data.get(key) != new_data.get(key):
                        changes[key] = {
                            'old': old_data.get(key),
                            'new': new_data.get(key)
                        }

                if changes:
                    AchievementsItemRevision.objects.create(
                        achievements_item=updated_item,
                        changes=changes,
                        updated_by=request.user
                    )

                # Return updated data
                response_serializer = AchievementsItemSerializer(updated_item)

                return Response({
                    'success': True,
                    'message': 'Achievement item updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)

        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating achievement item',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def delete_achievements_item(request, item_id):
    """
    DELETE /api/admin/home-page/achievements/items/{id}/
    Delete specific achievement item (soft delete)
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to delete achievement items.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Get the achievement item
        achievement_item = AchievementsItem.objects.filter(id=item_id, is_active=True).first()

        if not achievement_item:
            return Response({
                'success': False,
                'message': 'Achievement item not found',
                'error_code': 'ACHIEVEMENT_ITEM_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)

        # Store old data for revision tracking
        old_data = copy.deepcopy(achievement_item.structured_data)

        with transaction.atomic():
            # Soft delete the achievement item
            achievement_item.is_active = False
            achievement_item.updated_by = request.user
            achievement_item.save()

            # Create revision record
            AchievementsItemRevision.objects.create(
                achievements_item=achievement_item,
                changes={'action': 'deleted', 'old_data': old_data},
                updated_by=request.user
            )

        return Response({
            'success': True,
            'message': 'Achievement item deleted successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while deleting achievement item',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_achievements_items_en(request):
    """
    GET /api/home-page/achievements/items/en/
    Fetch all achievement items in English (Public endpoint - no authentication required)
    """
    try:
        # Get all active achievement items
        items = AchievementsItem.objects.filter(is_active=True)

        # Return English content only
        data = []
        for item in items:
            data.append({
                'id': item.id,
                'icon_name': item.icon_name,
                'achievement_value': item.achievement_value,
                'title': item.title_en,
                'created_at': item.created_at.isoformat(),
                'updated_at': item.updated_at.isoformat()
            })

        return Response({
            'success': True,
            'message': 'Achievement items retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching achievement items',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_achievements_items_ar(request):
    """
    GET /api/home-page/achievements/items/ar/
    Fetch all achievement items in Arabic (Public endpoint - no authentication required)
    """
    try:
        # Get all active achievement items
        items = AchievementsItem.objects.filter(is_active=True)

        # Return Arabic content only
        data = []
        for item in items:
            data.append({
                'id': item.id,
                'icon_name': item.icon_name,
                'achievement_value': item.achievement_value,
                'title': item.title_ar,
                'created_at': item.created_at.isoformat(),
                'updated_at': item.updated_at.isoformat()
            })

        return Response({
            'success': True,
            'message': 'Achievement items retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching achievement items',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





# ============================================================================
# LATEST ARTICLES SECTION VIEWS
# ============================================================================

@api_view(['GET', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def latest_articles_content_handler(request):
    """
    Combined handler for latest articles content operations
    GET /api/admin/home-page/latest-articles/ - Fetch current latest articles section content
    PUT/PATCH /api/admin/home-page/latest-articles/ - Update latest articles section content
    """
    if request.method == 'GET':
        return get_latest_articles_content(request)
    elif request.method in ['PUT', 'PATCH']:
        return update_latest_articles_content(request)


def get_latest_articles_content(request):
    """
    GET /api/admin/home-page/latest-articles/
    Fetch current latest articles section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to manage home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get the active latest articles section (latest one)
        latest_articles_section = LatestArticlesSection.objects.filter(is_active=True).first()
        
        if not latest_articles_section:
            return Response({
                'success': False,
                'message': 'Latest articles content not found',
                'error_code': 'LATEST_ARTICLES_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = LatestArticlesSectionSerializer(latest_articles_section)
        
        return Response({
            'success': True,
            'message': 'Latest articles content retrieved successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching latest articles content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_latest_articles_content(request):
    """
    PUT/PATCH /api/admin/home-page/latest-articles/
    Update latest articles section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get or create latest articles section
        latest_articles_section = LatestArticlesSection.objects.filter(is_active=True).first()
        
        if not latest_articles_section:
            # Create new latest articles section if none exists
            latest_articles_section = LatestArticlesSection(is_active=True)
        
        # Store old data for revision tracking
        old_data = copy.deepcopy(latest_articles_section.structured_data) if latest_articles_section.id else {}
        
        # Validate and update
        serializer = LatestArticlesSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            with transaction.atomic():
                # Update the latest articles section
                latest_articles_section.updated_by = request.user
                updated_section = serializer.update(latest_articles_section, serializer.validated_data)
                
                # Create revision record
                if old_data:
                    changes = {}
                    new_data = updated_section.structured_data
                    
                    # Track changes
                    for key in ['title', 'subtitle', 'description', 'button']:
                        if old_data.get(key) != new_data.get(key):
                            changes[key] = {
                                'old': old_data.get(key),
                                'new': new_data.get(key)
                            }
                    
                    if changes:
                        LatestArticlesSectionRevision.objects.create(
                            latest_articles_section=updated_section,
                            changes=changes,
                            updated_by=request.user
                        )
                
                # Return updated data
                response_serializer = LatestArticlesSectionSerializer(updated_section)
                
                return Response({
                    'success': True,
                    'message': 'Latest articles section updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating latest articles content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_latest_articles_section(request):
    """
    PATCH /api/admin/home-page/latest-articles/section/
    Update specific sections of latest articles content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get latest articles section
        latest_articles_section = LatestArticlesSection.objects.filter(is_active=True).first()
        
        if not latest_articles_section:
            return Response({
                'success': False,
                'message': 'Latest articles content not found',
                'error_code': 'LATEST_ARTICLES_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate section update
        serializer = LatestArticlesSectionSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            section = serializer.validated_data['section']
            data = serializer.validated_data['data']
            
            # Store old data for revision
            old_section_data = getattr(latest_articles_section.structured_data, section, {})
            
            # Update specific section
            with transaction.atomic():
                if section == 'badge':
                    latest_articles_section.badge_en = data['en']
                    latest_articles_section.badge_ar = data['ar']
                elif section == 'title':
                    latest_articles_section.title_en = data['en']
                    latest_articles_section.title_ar = data['ar']
                elif section == 'description':
                    latest_articles_section.description_en = data['en']
                    latest_articles_section.description_ar = data['ar']
                elif section == 'button':
                    latest_articles_section.button_text_en = data['text']['en']
                    latest_articles_section.button_text_ar = data['text']['ar']
                    latest_articles_section.button_link = data['link']
                
                latest_articles_section.updated_by = request.user
                latest_articles_section.save()
                
                # Create revision record
                changes = {
                    section: {
                        'old': old_section_data,
                        'new': data
                    }
                }
                
                LatestArticlesSectionRevision.objects.create(
                    latest_articles_section=latest_articles_section,
                    changes=changes,
                    updated_by=request.user
                )
            
            return Response({
                'success': True,
                'message': f'{section.title()} section updated successfully',
                'data': {
                    'section': section,
                    'updated_data': data,
                    'updated_at': latest_articles_section.updated_at.isoformat()
                }
            }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating section',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LatestArticlesRevisionPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'per_page'
    max_page_size = 50


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_latest_articles_history(request):
    """
    GET /api/admin/home-page/latest-articles/history/
    Get revision history for latest articles section
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to view home page content history.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get latest articles section
        latest_articles_section = LatestArticlesSection.objects.filter(is_active=True).first()
        
        if not latest_articles_section:
            return Response({
                'success': False,
                'message': 'Latest articles content not found',
                'error_code': 'LATEST_ARTICLES_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get revisions
        revisions = LatestArticlesSectionRevision.objects.filter(latest_articles_section=latest_articles_section)
        
        # Paginate results
        paginator = LatestArticlesRevisionPagination()
        paginated_revisions = paginator.paginate_queryset(revisions, request)
        
        serializer = LatestArticlesSectionRevisionSerializer(paginated_revisions, many=True)
        
        return Response({
            'success': True,
            'data': {
                'total': revisions.count(),
                'page': int(request.GET.get('page', 1)),
                'per_page': paginator.page_size,
                'revisions': serializer.data
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching history',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_latest_articles_content_en(request):
    """
    GET /api/home-page/latest-articles/en/
    Fetch current Latest Articles section content in English (Public endpoint - no authentication required)
    """
    try:
        # Get the active Latest Articles section (latest one)
        latest_articles_section = LatestArticlesSection.objects.filter(is_active=True).first()
        
        if not latest_articles_section:
            return Response({
                'success': False,
                'message': 'Latest articles content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return English content only
        data = {
            'id': latest_articles_section.id,
            'badge': latest_articles_section.badge_en,
            'title': latest_articles_section.title_en,
            'description': latest_articles_section.description_en,
            'button': {
                'text': latest_articles_section.button_text_en,
                'link': latest_articles_section.button_link
            },
            'created_at': latest_articles_section.created_at.isoformat(),
            'updated_at': latest_articles_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'Latest articles content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching latest articles content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_latest_articles_content_ar(request):
    """
    GET /api/home-page/latest-articles/ar/
    Fetch current Latest Articles section content in Arabic (Public endpoint - no authentication required)
    """
    try:
        # Get the active Latest Articles section (latest one)
        latest_articles_section = LatestArticlesSection.objects.filter(is_active=True).first()
        
        if not latest_articles_section:
            return Response({
                'success': False,
                'message': 'Latest articles content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return Arabic content only
        data = {
            'id': latest_articles_section.id,
            'badge': latest_articles_section.badge_ar,
            'title': latest_articles_section.title_ar,
            'description': latest_articles_section.description_ar,
            'button': {
                'text': latest_articles_section.button_text_ar,
                'link': latest_articles_section.button_link                
            },
            'created_at': latest_articles_section.created_at.isoformat(),
            'updated_at': latest_articles_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'Latest articles content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching latest articles content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





# ============================================================================
# TESTIMONIAL SECTION VIEWS
# ============================================================================

@api_view(['GET', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def testimonials_content_handler(request):
    """
    Combined handler for testimonials content operations
    GET /api/admin/home-page/testimonials/ - Fetch current testimonials section content
    PUT/PATCH /api/admin/home-page/testimonials/ - Update testimonials section content
    """
    if request.method == 'GET':
        return get_testimonials_content(request)
    elif request.method in ['PUT', 'PATCH']:
        return update_testimonials_content(request)


def get_testimonials_content(request):
    """
    GET /api/admin/home-page/testimonials/
    Fetch current testimonials section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to manage home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get the active testimonials section (latest one)
        testimonials_section = TestimonialsSection.objects.filter(is_active=True).first()
        
        if not testimonials_section:
            return Response({
                'success': False,
                'message': 'Testimonials content not found',
                'error_code': 'LATEST_ARTICLES_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = TestimonialsSectionSerializer(testimonials_section)
        
        return Response({
            'success': True,
            'message': 'Testimonials content retrieved successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching testimonials content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_testimonials_content(request):
    """
    PUT/PATCH /api/admin/home-page/testimonials/
    Update testimonials section content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get or create testimonials section
        testimonials_section = TestimonialsSection.objects.filter(is_active=True).first()
        
        if not testimonials_section:
            # Create new testimonials section if none exists
            testimonials_section = TestimonialsSection(is_active=True)
        
        # Store old data for revision tracking
        old_data = copy.deepcopy(testimonials_section.structured_data) if testimonials_section.id else {}
        
        # Validate and update
        serializer = TestimonialsSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            with transaction.atomic():
                # Update the testimonials section
                testimonials_section.updated_by = request.user
                updated_section = serializer.update(testimonials_section, serializer.validated_data)
                
                # Create revision record
                if old_data:
                    changes = {}
                    new_data = updated_section.structured_data
                    
                    # Track changes
                    for key in ['title', 'subtitle', 'description', 'button']:
                        if old_data.get(key) != new_data.get(key):
                            changes[key] = {
                                'old': old_data.get(key),
                                'new': new_data.get(key)
                            }
                    
                    if changes:
                        TestimonialsSectionRevision.objects.create(
                            testimonials_section=updated_section,
                            changes=changes,
                            updated_by=request.user
                        )
                
                # Return updated data
                response_serializer = TestimonialsSectionSerializer(updated_section)
                
                return Response({
                    'success': True,
                    'message': 'Testimonials section updated successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating testimonials content',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_testimonials_section(request):
    """
    PATCH /api/admin/home-page/testimonials/section/
    Update specific sections of testimonials content
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to update home page content.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get testimonials section
        testimonials_section = TestimonialsSection.objects.filter(is_active=True).first()
        
        if not testimonials_section:
            return Response({
                'success': False,
                'message': 'Testimonials content not found',
                'error_code': 'TESTIMONIALS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate section update
        serializer = TestimonialsSectionSectionUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            section = serializer.validated_data['section']
            data = serializer.validated_data['data']
            
            # Store old data for revision
            old_section_data = getattr(testimonials_section.structured_data, section, {})
            
            # Update specific section
            with transaction.atomic():
                if section == 'badge':
                    testimonials_section.badge_en = data['en']
                    testimonials_section.badge_ar = data['ar']
                elif section == 'title':
                    testimonials_section.title_en = data['en']
                    testimonials_section.title_ar = data['ar']
                elif section == 'description':
                    testimonials_section.description_en = data['en']
                    testimonials_section.description_ar = data['ar']
                
                testimonials_section.updated_by = request.user
                testimonials_section.save()
                
                # Create revision record
                changes = {
                    section: {
                        'old': old_section_data,
                        'new': data
                    }
                }
                
                TestimonialsSectionRevision.objects.create(
                    testimonials_section=testimonials_section,
                    changes=changes,
                    updated_by=request.user
                )
            
            return Response({
                'success': True,
                'message': f'{section.title()} section updated successfully',
                'data': {
                    'section': section,
                    'updated_data': data,
                    'updated_at': testimonials_section.updated_at.isoformat()
                }
            }, status=status.HTTP_200_OK)
        
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors,
                'error_code': 'VALIDATION_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while updating section',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestimonialsRevisionPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'per_page'
    max_page_size = 50


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_testimonials_history(request):
    """
    GET /api/admin/home-page/testimonials/history/
    Get revision history for testimonials section
    """
    # Check permissions
    if not check_homepage_permission(request.user):
        return Response({
            'success': False,
            'message': 'Permission denied. You don\'t have access to view home page content history.',
            'error_code': 'PERMISSION_DENIED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get testimonials section
        testimonials_section = TestimonialsSection.objects.filter(is_active=True).first()
        
        if not testimonials_section:
            return Response({
                'success': False,
                'message': 'Testimonials content not found',
                'error_code': 'TESTIMONIALS_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get revisions
        revisions = TestimonialsSectionRevision.objects.filter(testimonials_section=testimonials_section)
        
        # Paginate results
        paginator = TestimonialsRevisionPagination()
        paginated_revisions = paginator.paginate_queryset(revisions, request)
        
        serializer = TestimonialsSectionRevisionSerializer(paginated_revisions, many=True)
        
        return Response({
            'success': True,
            'data': {
                'total': revisions.count(),
                'page': int(request.GET.get('page', 1)),
                'per_page': paginator.page_size,
                'revisions': serializer.data
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching history',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_testimonials_content_en(request):
    """
    GET /api/home-page/testimonials/en/
    Fetch current Testimonials section content in English (Public endpoint - no authentication required)
    """
    try:
        # Get the active Testimonials section (latest one)
        testimonials_section = TestimonialsSection.objects.filter(is_active=True).first()
        
        if not testimonials_section:
            return Response({
                'success': False,
                'message': 'Testimonials content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return English content only
        data = {
            'id': testimonials_section.id,
            'badge': testimonials_section.badge_en,
            'title': testimonials_section.title_en,
            'description': testimonials_section.description_en,
            'created_at': testimonials_section.created_at.isoformat(),
            'updated_at': testimonials_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'Testimonials content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching testimonials content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([])  # No authentication required
def get_testimonials_content_ar(request):
    """
    GET /api/home-page/testimonials/ar/
    Fetch current Testimonials section content in Arabic (Public endpoint - no authentication required)
    """
    try:
        # Get the active Testimonials section (latest one)
        testimonials_section = TestimonialsSection.objects.filter(is_active=True).first()
        
        if not testimonials_section:
            return Response({
                'success': False,
                'message': 'Testimonials content not found',
                'error': 'No content available for the specified language'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return Arabic content only
        data = {
            'id': testimonials_section.id,
            'badge': testimonials_section.badge_ar,
            'title': testimonials_section.title_ar,
            'description': testimonials_section.description_ar,
            'created_at': testimonials_section.created_at.isoformat(),
            'updated_at': testimonials_section.updated_at.isoformat()
        }
        
        return Response({
            'success': True,
            'message': 'Testimonials content retrieved successfully',
            'data': data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Internal server error occurred while fetching testimonials content',
            'error': 'Server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


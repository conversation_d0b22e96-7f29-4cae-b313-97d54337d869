from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import AdminUser, PasswordResetToken


class AdminLoginSerializer(serializers.Serializer):
    """
    Serializer for admin login
    """
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    remember_me = serializers.BooleanField(default=False)
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            # Convert email to lowercase for consistency
            email = email.lower()
            
            # Try to authenticate the user
            user = authenticate(username=email, password=password)
            
            if user:
                if not user.is_active:
                    raise serializers.ValidationError(
                        {'non_field_errors': ['User account is disabled.']}
                    )
                attrs['user'] = user
            else:
                raise serializers.ValidationError(
                    {'non_field_errors': ['Invalid email or password']}
                )
        else:
            raise serializers.ValidationError(
                {'non_field_errors': ['Must include email and password']}
            )
        
        return attrs


class AdminUserSerializer(serializers.ModelSerializer):
    """
    Serializer for admin user data
    """
    permissions = serializers.ReadOnlyField()
    avatar = serializers.SerializerMethodField()
    
    class Meta:
        model = AdminUser
        fields = [
            'id', 'email', 'first_name', 'last_name', 
            'role', 'permissions', 'avatar', 'last_login'
        ]
        read_only_fields = ['id', 'last_login']
    
    def get_avatar(self, obj):
        if obj.avatar:
            return obj.avatar.url
        return '/media/avatars/admin.jpg'  # Default avatar


class TokenRefreshSerializer(serializers.Serializer):
    """
    Serializer for token refresh
    """
    refresh = serializers.CharField()


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for password reset request
    """
    email = serializers.EmailField()
    
    def validate_email(self, value):
        email = value.lower()
        try:
            user = AdminUser.objects.get(email=email, is_active=True)
        except AdminUser.DoesNotExist:
            # Don't reveal if email exists or not for security
            pass
        return email


class LogoutSerializer(serializers.Serializer):
    """
    Serializer for logout
    """
    refresh = serializers.CharField()


class AdminUserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for admin user profile (GET /me endpoint)
    """
    permissions = serializers.ReadOnlyField()
    avatar = serializers.SerializerMethodField()
    
    class Meta:
        model = AdminUser
        fields = [
            'id', 'email', 'first_name', 'last_name', 
            'role', 'permissions', 'avatar'
        ]
        read_only_fields = ['id', 'email']
    
    def get_avatar(self, obj):
        if obj.avatar:
            return obj.avatar.url
        return '/media/avatars/admin.jpg'  # Default avatar


class AdminUserProfileUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating admin user profile (PUT /profile endpoint)
    """
    permissions = serializers.ReadOnlyField()
    avatar = serializers.ImageField(required=False, allow_null=True)
    avatar_url = serializers.SerializerMethodField()
    
    class Meta:
        model = AdminUser
        fields = [
            'id', 'email', 'first_name', 'last_name', 
            'role', 'permissions', 'avatar', 'avatar_url', 'last_login'
        ]
        read_only_fields = ['id', 'email', 'role', 'last_login']
    
    def get_avatar_url(self, obj):
        if obj.avatar:
            return obj.avatar.url
        return '/media/avatars/admin.jpg'  # Default avatar
    
    def validate_first_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("First name cannot be empty.")
        return value.strip()
    
    def validate_last_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("Last name cannot be empty.")
        return value.strip()
    
    def validate_avatar(self, value):
        if value:
            # Check file size (max 5MB)
            if value.size > 5 * 1024 * 1024:
                raise serializers.ValidationError("Avatar file size cannot exceed 5MB.")
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if value.content_type not in allowed_types:
                raise serializers.ValidationError("Avatar must be a JPEG, PNG, or GIF image.")
        
        return value
    
    def update(self, instance, validated_data):
        # Handle avatar deletion if avatar is explicitly set to None
        if 'avatar' in validated_data and validated_data['avatar'] is None:
            if instance.avatar:
                instance.avatar.delete(save=False)
        
        return super().update(instance, validated_data)


class AvatarUploadSerializer(serializers.ModelSerializer):
    """
    Serializer specifically for avatar upload
    """
    avatar = serializers.ImageField(required=True)
    
    class Meta:
        model = AdminUser
        fields = ['avatar']
    
    def validate_avatar(self, value):
        # Check file size (max 5MB)
        if value.size > 5 * 1024 * 1024:
            raise serializers.ValidationError("File size must be less than 5MB.")
        
        # Check file type
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        if value.content_type not in allowed_types:
            raise serializers.ValidationError("Only JPEG, PNG, and GIF files are allowed.")
        
        return value
    
    def update(self, instance, validated_data):
        # Delete old avatar if exists
        if instance.avatar:
            instance.avatar.delete(save=False)
        
        return super().update(instance, validated_data) 
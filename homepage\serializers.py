from rest_framework import serializers
from django.core.validators import URLValidator
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import HeroSection, HeroSectionRevision, AboutUsSection, AboutUsSectionRevision, FeaturedProjectsSection, FeaturedProjectsSectionRevision, AchievementsSection, AchievementsSectionRevision, AchievementsItem, AchievementsItemRevision


class HeroSectionSerializer(serializers.ModelSerializer):
    """
    Serializer for Hero Section with nested structure support
    """
    badge = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    buttons = serializers.SerializerMethodField()
    bottomText = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = HeroSection
        fields = [
            'id', 'badge', 'title', 'description', 'buttons', 'bottomText',
            'created_at', 'updated_at', 'updated_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_badge(self, obj):
        return {
            'en': obj.badge_en,
            'ar': obj.badge_ar
        }
    
    def get_title(self, obj):
        return {
            'line1': {
                'en': obj.title_line1_en,
                'ar': obj.title_line1_ar
            },
            'line2': {
                'en': obj.title_line2_en,
                'ar': obj.title_line2_ar
            },
            'line3': {
                'en': obj.title_line3_en,
                'ar': obj.title_line3_ar
            }
        }
    
    def get_description(self, obj):
        return {
            'en': obj.description_en,
            'ar': obj.description_ar
        }
    
    def get_buttons(self, obj):
        return {
            'primary': {
                'text': {
                    'en': obj.primary_button_text_en,
                    'ar': obj.primary_button_text_ar
                },
                'link': obj.primary_button_link
            },
            'secondary': {
                'text': {
                    'en': obj.secondary_button_text_en,
                    'ar': obj.secondary_button_text_ar
                },
                'link': obj.secondary_button_link
            }
        }
    
    def get_bottomText(self, obj):
        return {
            'en': obj.bottom_text_en,
            'ar': obj.bottom_text_ar
        }
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class HeroSectionUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating Hero Section with nested validation
    """
    badge = serializers.DictField(required=True)
    title = serializers.DictField(required=True)
    description = serializers.DictField(required=True)
    buttons = serializers.DictField(required=True)
    bottomText = serializers.DictField(required=True)
    
    def validate_badge(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Badge must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 200:
            raise serializers.ValidationError({"en": ["Badge text cannot exceed 200 characters"]})
        if len(value['ar']) > 200:
            raise serializers.ValidationError({"ar": ["Badge text cannot exceed 200 characters"]})
        
        return value
    
    def validate_title(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Title must be an object")
        
        required_lines = ['line1', 'line2', 'line3']
        for line in required_lines:
            if line not in value:
                raise serializers.ValidationError({line: ["This field is required"]})
            
            line_data = value[line]
            if not isinstance(line_data, dict):
                raise serializers.ValidationError({line: ["Must be an object with 'en' and 'ar' fields"]})
            
            if 'en' not in line_data or not line_data['en']:
                raise serializers.ValidationError({f"{line}.en": ["This field is required"]})
            if 'ar' not in line_data or not line_data['ar']:
                raise serializers.ValidationError({f"{line}.ar": ["This field is required"]})
            
            if len(line_data['en']) > 100:
                raise serializers.ValidationError({f"{line}.en": ["Title line cannot exceed 100 characters"]})
            if len(line_data['ar']) > 100:
                raise serializers.ValidationError({f"{line}.ar": ["Title line cannot exceed 100 characters"]})
        
        return value
    
    def validate_description(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Description must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 500:
            raise serializers.ValidationError({"en": ["Description cannot exceed 500 characters"]})
        if len(value['ar']) > 500:
            raise serializers.ValidationError({"ar": ["Description cannot exceed 500 characters"]})
        
        return value
    
    def validate_buttons(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Buttons must be an object")
        
        required_buttons = ['primary', 'secondary']
        url_validator = URLValidator()
        
        for button_type in required_buttons:
            if button_type not in value:
                raise serializers.ValidationError({button_type: ["This field is required"]})
            
            button_data = value[button_type]
            if not isinstance(button_data, dict):
                raise serializers.ValidationError({button_type: ["Must be an object"]})
            
            # Validate text
            if 'text' not in button_data:
                raise serializers.ValidationError({f"{button_type}.text": ["This field is required"]})
            
            text_data = button_data['text']
            if not isinstance(text_data, dict):
                raise serializers.ValidationError({f"{button_type}.text": ["Must be an object with 'en' and 'ar' fields"]})
            
            if 'en' not in text_data or not text_data['en']:
                raise serializers.ValidationError({f"{button_type}.text.en": ["This field is required"]})
            if 'ar' not in text_data or not text_data['ar']:
                raise serializers.ValidationError({f"{button_type}.text.ar": ["This field is required"]})
            
            if len(text_data['en']) > 50:
                raise serializers.ValidationError({f"{button_type}.text.en": ["Button text cannot exceed 50 characters"]})
            if len(text_data['ar']) > 50:
                raise serializers.ValidationError({f"{button_type}.text.ar": ["Button text cannot exceed 50 characters"]})
            
            # Validate link
            if 'link' not in button_data or not button_data['link']:
                raise serializers.ValidationError({f"{button_type}.link": ["This field is required"]})
            
            link = button_data['link']
            if not link.startswith('/'):
                try:
                    url_validator(link)
                except DjangoValidationError:
                    raise serializers.ValidationError({f"{button_type}.link": ["Invalid URL format"]})
        
        return value
    
    def validate_bottomText(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Bottom text must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Bottom text cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Bottom text cannot exceed 100 characters"]})
        
        return value
    
    def update(self, instance, validated_data):
        """Update the hero section instance with validated data"""
        # Update badge
        badge = validated_data['badge']
        instance.badge_en = badge['en']
        instance.badge_ar = badge['ar']
        
        # Update title
        title = validated_data['title']
        instance.title_line1_en = title['line1']['en']
        instance.title_line1_ar = title['line1']['ar']
        instance.title_line2_en = title['line2']['en']
        instance.title_line2_ar = title['line2']['ar']
        instance.title_line3_en = title['line3']['en']
        instance.title_line3_ar = title['line3']['ar']
        
        # Update description
        description = validated_data['description']
        instance.description_en = description['en']
        instance.description_ar = description['ar']
        
        # Update buttons
        buttons = validated_data['buttons']
        instance.primary_button_text_en = buttons['primary']['text']['en']
        instance.primary_button_text_ar = buttons['primary']['text']['ar']
        instance.primary_button_link = buttons['primary']['link']
        instance.secondary_button_text_en = buttons['secondary']['text']['en']
        instance.secondary_button_text_ar = buttons['secondary']['text']['ar']
        instance.secondary_button_link = buttons['secondary']['link']
        
        # Update bottom text
        bottom_text = validated_data['bottomText']
        instance.bottom_text_en = bottom_text['en']
        instance.bottom_text_ar = bottom_text['ar']
        
        instance.save()
        return instance


class HeroSectionSectionUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating specific sections of hero content
    """
    section = serializers.ChoiceField(choices=['badge', 'title', 'description', 'buttons', 'bottomText'])
    data = serializers.DictField()
    
    def validate(self, attrs):
        section = attrs['section']
        data = attrs['data']
        
        # Validate based on section type
        if section == 'badge':
            self._validate_badge_data(data)
        elif section == 'title':
            self._validate_title_data(data)
        elif section == 'description':
            self._validate_description_data(data)
        elif section == 'buttons':
            self._validate_buttons_data(data)
        elif section == 'bottomText':
            self._validate_bottom_text_data(data)
        
        return attrs
    
    def _validate_badge_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})
    
    def _validate_title_data(self, data):
        required_lines = ['line1', 'line2', 'line3']
        for line in required_lines:
            if line not in data:
                raise serializers.ValidationError({f"data.{line}": ["This field is required"]})
            
            line_data = data[line]
            if 'en' not in line_data or not line_data['en']:
                raise serializers.ValidationError({f"data.{line}.en": ["This field is required"]})
            if 'ar' not in line_data or not line_data['ar']:
                raise serializers.ValidationError({f"data.{line}.ar": ["This field is required"]})
    
    def _validate_description_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})
    
    def _validate_buttons_data(self, data):
        # Similar validation as in HeroSectionUpdateSerializer
        pass
    
    def _validate_bottom_text_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})


class HeroSectionRevisionSerializer(serializers.ModelSerializer):
    """
    Serializer for Hero Section revision history
    """
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = HeroSectionRevision
        fields = ['id', 'changes', 'updated_by', 'updated_at']
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class AboutUsSectionSerializer(serializers.ModelSerializer):
    """
    Serializer for About Us Section with nested structure support
    """
    badge = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    subtitle = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    imageOverlay = serializers.SerializerMethodField()
    mainImage = serializers.SerializerMethodField()
    mainImageFile = serializers.SerializerMethodField()
    imageAltText = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = AboutUsSection
        fields = [
            'id', 'badge', 'title', 'subtitle', 'description', 'imageOverlay', 
            'mainImage', 'mainImageFile', 'imageAltText',
            'created_at', 'updated_at', 'updated_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_badge(self, obj):
        return {
            'en': obj.badge_en,
            'ar': obj.badge_ar
        }
    
    def get_title(self, obj):
        return {
            'en': obj.title_en,
            'ar': obj.title_ar
        }
    
    def get_subtitle(self, obj):
        return {
            'en': obj.subtitle_en,
            'ar': obj.subtitle_ar
        }
    
    def get_description(self, obj):
        return {
            'en': obj.description_en,
            'ar': obj.description_ar
        }
    
    def get_imageOverlay(self, obj):
        return {
            'badge': {
                'en': obj.image_overlay_badge_en,
                'ar': obj.image_overlay_badge_ar
            },
            'text': {
                'en': obj.image_overlay_text_en,
                'ar': obj.image_overlay_text_ar
            }
        }
    
    def get_mainImage(self, obj):
        return obj.main_image
    
    def get_mainImageFile(self, obj):
        return obj.main_image_file.url if obj.main_image_file else None
    
    def get_imageAltText(self, obj):
        return {
            'en': obj.image_alt_text_en,
            'ar': obj.image_alt_text_ar
        }
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class AboutUsSectionUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating About Us Section with nested validation
    Supports partial updates - only provided fields will be updated
    """
    badge = serializers.DictField(required=False)
    title = serializers.DictField(required=False)
    subtitle = serializers.DictField(required=False)
    description = serializers.DictField(required=False)
    imageOverlay = serializers.DictField(required=False)
    mainImageFile = serializers.ImageField(required=False)
    imageAltText = serializers.DictField(required=False)
    
    def validate_badge(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Badge must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en'].strip()) < 3:
            raise serializers.ValidationError({"en": ["Badge must be at least 3 characters long"]})
        if len(value['ar'].strip()) < 3:
            raise serializers.ValidationError({"ar": ["Badge must be at least 3 characters long"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Badge text cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Badge text cannot exceed 100 characters"]})
        
        return value
    
    def validate_title(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Title must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en'].strip()) < 2:
            raise serializers.ValidationError({"en": ["Title must be at least 2 characters long"]})
        if len(value['ar'].strip()) < 2:
            raise serializers.ValidationError({"ar": ["Title must be at least 2 characters long"]})
        
        if len(value['en']) > 50:
            raise serializers.ValidationError({"en": ["Title cannot exceed 50 characters"]})
        if len(value['ar']) > 50:
            raise serializers.ValidationError({"ar": ["Title cannot exceed 50 characters"]})
        
        return value
    
    def validate_subtitle(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Subtitle must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en'].strip()) < 2:
            raise serializers.ValidationError({"en": ["Subtitle must be at least 2 characters long"]})
        if len(value['ar'].strip()) < 2:
            raise serializers.ValidationError({"ar": ["Subtitle must be at least 2 characters long"]})
        
        if len(value['en']) > 50:
            raise serializers.ValidationError({"en": ["Subtitle cannot exceed 50 characters"]})
        if len(value['ar']) > 50:
            raise serializers.ValidationError({"ar": ["Subtitle cannot exceed 50 characters"]})
        
        return value
    
    def validate_description(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Description must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en'].strip()) < 10:
            raise serializers.ValidationError({"en": ["Description must be at least 10 characters long"]})
        if len(value['ar'].strip()) < 10:
            raise serializers.ValidationError({"ar": ["Description must be at least 10 characters long"]})
        
        if len(value['en']) > 1000:
            raise serializers.ValidationError({"en": ["Description cannot exceed 1000 characters"]})
        if len(value['ar']) > 1000:
            raise serializers.ValidationError({"ar": ["Description cannot exceed 1000 characters"]})
        
        return value
    
    def validate_imageOverlay(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Image overlay must be an object")
        
        # Validate badge
        if 'badge' not in value:
            raise serializers.ValidationError({"badge": ["This field is required"]})
        
        badge_data = value['badge']
        if not isinstance(badge_data, dict):
            raise serializers.ValidationError({"badge": ["Must be an object with 'en' and 'ar' fields"]})
        
        if 'en' not in badge_data or not badge_data['en']:
            raise serializers.ValidationError({"badge.en": ["This field is required"]})
        if 'ar' not in badge_data or not badge_data['ar']:
            raise serializers.ValidationError({"badge.ar": ["This field is required"]})
        
        if len(badge_data['en'].strip()) < 3:
            raise serializers.ValidationError({"badge.en": ["Badge must be at least 3 characters long"]})
        if len(badge_data['ar'].strip()) < 3:
            raise serializers.ValidationError({"badge.ar": ["Badge must be at least 3 characters long"]})
        
        if len(badge_data['en']) > 50:
            raise serializers.ValidationError({"badge.en": ["Badge cannot exceed 50 characters"]})
        if len(badge_data['ar']) > 50:
            raise serializers.ValidationError({"badge.ar": ["Badge cannot exceed 50 characters"]})
        
        # Validate text
        if 'text' not in value:
            raise serializers.ValidationError({"text": ["This field is required"]})
        
        text_data = value['text']
        if not isinstance(text_data, dict):
            raise serializers.ValidationError({"text": ["Must be an object with 'en' and 'ar' fields"]})
        
        if 'en' not in text_data or not text_data['en']:
            raise serializers.ValidationError({"text.en": ["This field is required"]})
        if 'ar' not in text_data or not text_data['ar']:
            raise serializers.ValidationError({"text.ar": ["This field is required"]})
        
        if len(text_data['en'].strip()) < 5:
            raise serializers.ValidationError({"text.en": ["Text must be at least 5 characters long"]})
        if len(text_data['ar'].strip()) < 5:
            raise serializers.ValidationError({"text.ar": ["Text must be at least 5 characters long"]})
        
        if len(text_data['en']) > 100:
            raise serializers.ValidationError({"text.en": ["Text cannot exceed 100 characters"]})
        if len(text_data['ar']) > 100:
            raise serializers.ValidationError({"text.ar": ["Text cannot exceed 100 characters"]})
        
        return value
    
    def validate_mainImageFile(self, value):
        # Image file is now optional for updates
        if value:
            # Validate file size (5MB limit)
            if value.size > 5 * 1024 * 1024:
                raise serializers.ValidationError("File size exceeds maximum limit of 5MB")
            
            # Validate file type
            allowed_types = ['image/jpeg', 'image/png', 'image/webp']
            if value.content_type not in allowed_types:
                raise serializers.ValidationError("File type not supported. Please upload JPEG, PNG, or WebP images.")
        
        return value
    
    def validate_imageAltText(self, value):
        if value and not isinstance(value, dict):
            raise serializers.ValidationError("Image alt text must be an object")
        
        if value:
            if 'en' in value and len(value['en']) > 255:
                raise serializers.ValidationError({"en": ["Alt text cannot exceed 255 characters"]})
            if 'ar' in value and len(value['ar']) > 255:
                raise serializers.ValidationError({"ar": ["Alt text cannot exceed 255 characters"]})
        
        return value
    
    def update(self, instance, validated_data):
        """Update About Us section instance with validated data"""
        # Update badge (if provided)
        if 'badge' in validated_data:
            badge_data = validated_data['badge']
            instance.badge_en = badge_data['en']
            instance.badge_ar = badge_data['ar']
        
        # Update title (if provided)
        if 'title' in validated_data:
            title_data = validated_data['title']
            instance.title_en = title_data['en']
            instance.title_ar = title_data['ar']
        
        # Update subtitle (if provided)
        if 'subtitle' in validated_data:
            subtitle_data = validated_data['subtitle']
            instance.subtitle_en = subtitle_data['en']
            instance.subtitle_ar = subtitle_data['ar']
        
        # Update description (if provided)
        if 'description' in validated_data:
            description_data = validated_data['description']
            instance.description_en = description_data['en']
            instance.description_ar = description_data['ar']
        
        # Update image overlay (if provided)
        if 'imageOverlay' in validated_data:
            image_overlay_data = validated_data['imageOverlay']
            if 'badge' in image_overlay_data:
                badge_overlay = image_overlay_data['badge']
                instance.image_overlay_badge_en = badge_overlay['en']
                instance.image_overlay_badge_ar = badge_overlay['ar']
            
            if 'text' in image_overlay_data:
                text_overlay = image_overlay_data['text']
                instance.image_overlay_text_en = text_overlay['en']
                instance.image_overlay_text_ar = text_overlay['ar']
        
        # Update image file (if provided)
        if 'mainImageFile' in validated_data:
            main_image_file = validated_data['mainImageFile']
            # Delete old file if exists
            if instance.main_image_file:
                instance.main_image_file.delete(save=False)
            instance.main_image_file = main_image_file
        
        # Update alt text (if provided)
        if 'imageAltText' in validated_data:
            image_alt_text = validated_data['imageAltText']
            if image_alt_text:
                instance.image_alt_text_en = image_alt_text.get('en', instance.image_alt_text_en)
                instance.image_alt_text_ar = image_alt_text.get('ar', instance.image_alt_text_ar)
        
        instance.save()
        return instance


class AboutUsSectionRevisionSerializer(serializers.ModelSerializer):
    """
    Serializer for About Us section revision history
    """
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = AboutUsSectionRevision
        fields = ['id', 'changes', 'updated_by', 'updated_at']
        read_only_fields = ['id', 'updated_at']
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class AboutUsImageUploadSerializer(serializers.ModelSerializer):
    """
    Serializer specifically for About Us image upload (following avatar upload pattern)
    """
    image = serializers.ImageField(required=True, source='main_image_file')
    
    class Meta:
        model = AboutUsSection
        fields = ['image']
    
    def validate_image(self, value):
        # Check file size (max 5MB)
        if value.size > 5 * 1024 * 1024:
            raise serializers.ValidationError("File size must be less than 5MB.")
        
        # Check file type
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        if value.content_type not in allowed_types:
            raise serializers.ValidationError("Only JPEG, PNG, and GIF files are allowed.")
        
        return value
    
    def update(self, instance, validated_data):
        # Delete old image file if exists
        if instance.main_image_file:
            instance.main_image_file.delete(save=False)
        
        return super().update(instance, validated_data)


class FeaturedProjectsSectionSerializer(serializers.ModelSerializer):
    """
    Serializer for Featured Projects Section with nested structure support
    """
    title = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    button = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = FeaturedProjectsSection
        fields = [
            'id', 'title', 'description', 'button',
            'created_at', 'updated_at', 'updated_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_title(self, obj):
        return {
            'en': obj.title_en,
            'ar': obj.title_ar
        }
    
    def get_description(self, obj):
        return {
            'en': obj.description_en,
            'ar': obj.description_ar
        }
    
    def get_button(self, obj):
        return {
            'text': {
                'en': obj.button_text_en,
                'ar': obj.button_text_ar
            },
            'link': obj.button_link
        }
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class FeaturedProjectsSectionUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating Featured Projects Section with nested validation
    """
    title = serializers.DictField(required=True)
    description = serializers.DictField(required=True)
    button = serializers.DictField(required=True)
    
    def validate_title(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Title must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Title cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Title cannot exceed 100 characters"]})
        
        return value
    
    
    def validate_description(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Description must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 500:
            raise serializers.ValidationError({"en": ["Description cannot exceed 500 characters"]})
        if len(value['ar']) > 500:
            raise serializers.ValidationError({"ar": ["Description cannot exceed 500 characters"]})
        
        return value
    
    def validate_button(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Button must be an object")
        
        # Validate text
        if 'text' not in value:
            raise serializers.ValidationError({"text": ["This field is required"]})
        
        text_data = value['text']
        if not isinstance(text_data, dict):
            raise serializers.ValidationError({"text": ["Must be an object with 'en' and 'ar' fields"]})
        
        if 'en' not in text_data or not text_data['en']:
            raise serializers.ValidationError({"text.en": ["This field is required"]})
        if 'ar' not in text_data or not text_data['ar']:
            raise serializers.ValidationError({"text.ar": ["This field is required"]})
        
        if len(text_data['en']) > 50:
            raise serializers.ValidationError({"text.en": ["Button text cannot exceed 50 characters"]})
        if len(text_data['ar']) > 50:
            raise serializers.ValidationError({"text.ar": ["Button text cannot exceed 50 characters"]})
        
        # Validate link
        if 'link' not in value or not value['link']:
            raise serializers.ValidationError({"link": ["This field is required"]})
        
        link = value['link']
        url_validator = URLValidator()
        if not link.startswith('/'):
            try:
                url_validator(link)
            except DjangoValidationError:
                raise serializers.ValidationError({"link": ["Enter a valid URL"]})
        
        return value
    
    def update(self, instance, validated_data):
        """Update the featured projects section instance with validated data"""
        # Update title
        title = validated_data['title']
        instance.title_en = title['en']
        instance.title_ar = title['ar']
        
        
        # Update description
        description = validated_data['description']
        instance.description_en = description['en']
        instance.description_ar = description['ar']
        
        # Update button
        button = validated_data['button']
        instance.button_text_en = button['text']['en']
        instance.button_text_ar = button['text']['ar']
        instance.button_link = button['link']
        
        instance.save()
        return instance


class FeaturedProjectsSectionSectionUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating specific sections of featured projects content
    """
    section = serializers.ChoiceField(choices=['title', 'subtitle', 'description', 'button'])
    data = serializers.DictField()
    
    def validate(self, attrs):
        section = attrs['section']
        data = attrs['data']
        
        # Validate based on section type
        if section == 'title':
            self._validate_title_data(data)
        elif section == 'subtitle':
            self._validate_subtitle_data(data)
        elif section == 'description':
            self._validate_description_data(data)
        elif section == 'button':
            self._validate_button_data(data)
        
        return attrs
    
    def _validate_title_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})
    
    def _validate_subtitle_data(self, data):
        # Subtitle is optional, so we don't require both languages
        if 'en' in data and len(data['en']) > 150:
            raise serializers.ValidationError({"data.en": ["Subtitle cannot exceed 150 characters"]})
        if 'ar' in data and len(data['ar']) > 150:
            raise serializers.ValidationError({"data.ar": ["Subtitle cannot exceed 150 characters"]})
    
    def _validate_description_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})
    
    def _validate_button_data(self, data):
        if 'text' not in data:
            raise serializers.ValidationError({"data.text": ["This field is required"]})
        if 'link' not in data:
            raise serializers.ValidationError({"data.link": ["This field is required"]})


class FeaturedProjectsSectionRevisionSerializer(serializers.ModelSerializer):
    """
    Serializer for Featured Projects Section revision history
    """
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = FeaturedProjectsSectionRevision
        fields = ['id', 'changes', 'updated_by', 'updated_at']
        read_only_fields = ['id', 'updated_at']
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None 
    

class AchievementsSectionSerializer(serializers.ModelSerializer):
    """
    Serializer for Achievements Section with nested structure support
    """
    badge = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = AchievementsSection
        fields = ['id', 'badge', 'title', 'description', 'created_at', 'updated_at', 'updated_by']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_badge(self, obj):
        return {
            'en': obj.badge_en,
            'ar': obj.badge_ar
        }
    
    def get_title(self, obj):
        return {
            'en': obj.title_en,
            'ar': obj.title_ar
        }
    
    def get_description(self, obj):
        return {
            'en': obj.description_en,
            'ar': obj.description_ar
        }
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class AchievementsSectionUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating Achievements Section with nested validation
    """
    badge = serializers.DictField(required=True)
    title = serializers.DictField(required=True)
    description = serializers.DictField(required=True)
    
    def validate_badge(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Badge must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Badge cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Badge cannot exceed 100 characters"]})
        
        return value
    
    def validate_title(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Title must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Title cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Title cannot exceed 100 characters"]})
        
        return value
    
    def validate_description(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Description must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 500:
            raise serializers.ValidationError({"en": ["Description cannot exceed 500 characters"]})
        if len(value['ar']) > 500:
            raise serializers.ValidationError({"ar": ["Description cannot exceed 500 characters"]})
        
        return value
    
    def update(self, instance, validated_data):
        """Update the achievements section instance with validated data"""
        # Update badge
        badge = validated_data['badge']
        instance.badge_en = badge['en']
        instance.badge_ar = badge['ar']
        
        # Update title
        title = validated_data['title']
        instance.title_en = title['en']
        instance.title_ar = title['ar']
        
        # Update description
        description = validated_data['description']
        instance.description_en = description['en']
        instance.description_ar = description['ar']
        
        instance.save()
        return instance


class AchievementsSectionRevisionSerializer(serializers.ModelSerializer):
    """
    Serializer for Achievements Section revision history
    """
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = AchievementsSectionRevision
        fields = ['id', 'changes', 'updated_by', 'updated_at']
        read_only_fields = ['id', 'updated_at']
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class AchievementsItemSerializer(serializers.ModelSerializer):
    """
    Serializer for Achievements Item with nested structure support
    """
    icon_name = serializers.SerializerMethodField()
    achievement_value = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = AchievementsItem
        fields = ['id', 'icon_name', 'achievement_value', 'title', 'created_at', 'updated_at', 'updated_by']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_icon_name(self, obj):
        return {
            'en': obj.icon_name_en,
            'ar': obj.icon_name_ar
        }
    
    def get_achievement_value(self, obj):
        return {
            'en': obj.achievement_value_en,
            'ar': obj.achievement_value_ar
        }
    
    def get_title(self, obj):
        return {
            'en': obj.title_en,
            'ar': obj.title_ar
        }
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class AchievementsItemUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating Achievements Item with nested validation
    """
    icon_name = serializers.DictField(required=True)
    achievement_value = serializers.DictField(required=True)
    title = serializers.DictField(required=True)

    def validate_icon_name(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Icon Name must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Icon name cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Icon name cannot exceed 100 characters"]})
        
        return value
    
    def validate_achievement_value(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Achievement Value must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Achievement value cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Achievement value cannot exceed 100 characters"]})
        
        return value
    
    def validate_title(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Title must be an object")
        
        if 'en' not in value or not value['en']:
            raise serializers.ValidationError({"en": ["This field is required"]})
        if 'ar' not in value or not value['ar']:
            raise serializers.ValidationError({"ar": ["This field is required"]})
        
        if len(value['en']) > 100:
            raise serializers.ValidationError({"en": ["Title cannot exceed 100 characters"]})
        if len(value['ar']) > 100:
            raise serializers.ValidationError({"ar": ["Title cannot exceed 100 characters"]})
        
        return value
    
    def update(self, instance, validated_data):
        """Update the achievements item instance with validated data"""
        # Update icon name
        icon_name = validated_data['icon_name']
        instance.icon_name_en = icon_name['en']
        instance.icon_name_ar = icon_name['ar']
        
        # Update achievement value
        achievement_value = validated_data['achievement_value']
        instance.achievement_value_en = achievement_value['en']
        instance.achievement_value_ar = achievement_value['ar']
        
        # Update title
        title = validated_data['title']
        instance.title_en = title['en']
        instance.title_ar = title['ar']
        
        instance.save()
        return instance


class AchievementsItemRevisionSerializer(serializers.ModelSerializer):
    """
    Serializer for Achievements Item revision history
    """
    updated_by = serializers.SerializerMethodField()
    
    class Meta:
        model = AchievementsItemRevision
        fields = ['id', 'changes', 'updated_by', 'updated_at']
        read_only_fields = ['id', 'updated_at']
    
    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                'id': obj.updated_by.id,
                'email': obj.updated_by.email,
                'first_name': obj.updated_by.first_name,
                'last_name': obj.updated_by.last_name
            }
        return None


class AchievementsItemSectionUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating specific sections of Achievements Item content
    """
    section = serializers.ChoiceField(choices=['icon_name', 'achievement_value', 'title'])
    data = serializers.DictField()
    
    def validate(self, attrs):
        section = attrs['section']
        data = attrs['data']
        
        # Validate based on section type
        if section == 'icon_name':
            self._validate_icon_name_data(data)
        elif section == 'achievement_value':
            self._validate_achievement_value_data(data)
        elif section == 'title':
            self._validate_title_data(data)
        
        return attrs
    
    def _validate_icon_name_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})
        
    def _validate_achievement_value_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})
        
    def _validate_title_data(self, data):
        if 'en' not in data or not data['en']:
            raise serializers.ValidationError({"data.en": ["This field is required"]})
        if 'ar' not in data or not data['ar']:
            raise serializers.ValidationError({"data.ar": ["This field is required"]})
    
    def update(self, instance, validated_data):
        """Update specific section of achievements item"""
        section = validated_data['section']
        data = validated_data['data']
        
        if section == 'icon_name':
            instance.icon_name_en = data['en']
            instance.icon_name_ar = data['ar']
        elif section == 'achievement_value':
            instance.achievement_value_en = data['en']
            instance.achievement_value_ar = data['ar']
        elif section == 'title':
            instance.title_en = data['en']
            instance.title_ar = data['ar']
        
        instance.save()
        return instance


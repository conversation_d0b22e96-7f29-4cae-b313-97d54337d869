from django.contrib import admin
from .models import (
    HeroSec<PERSON>,
    HeroSectionRevision,
    AboutUsSection,
    AboutUsSectionRevision,
    FeaturedProjectsSection,
    FeaturedProjectsSectionRevision,
    AchievementsSection,
    AchievementsSectionRevision,
    AchievementsItem,
    AchievementsItemRevision,
    LatestArticlesSection,
    LatestArticlesSectionRevision,
    TestimonialsSection,
    TestimonialsSectionRevision
)


@admin.register(HeroSection)
class HeroSectionAdmin(admin.ModelAdmin):
    """
    Admin interface for Hero Section
    """
    list_display = ('id', 'badge_en', 'is_active', 'updated_by', 'updated_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('badge_en', 'badge_ar', 'description_en', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Badge', {
            'fields': ('badge_en', 'badge_ar')
        }),
        ('Title', {
            'fields': (
                ('title_line1_en', 'title_line1_ar'),
                ('title_line2_en', 'title_line2_ar'),
                ('title_line3_en', 'title_line3_ar'),
            )
        }),
        ('Description', {
            'fields': ('description_en', 'description_ar')
        }),
        ('Primary Button', {
            'fields': (
                ('primary_button_text_en', 'primary_button_text_ar'),
                'primary_button_link'
            )
        }),
        ('Secondary Button', {
            'fields': (
                ('secondary_button_text_en', 'secondary_button_text_ar'),
                'secondary_button_link'
            )
        }),
        ('Bottom Text', {
            'fields': ('bottom_text_en', 'bottom_text_ar')
        }),
        ('Metadata', {
            'fields': ('is_active', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(HeroSectionRevision)
class HeroSectionRevisionAdmin(admin.ModelAdmin):
    """
    Admin interface for Hero Section Revisions
    """
    list_display = ('id', 'hero_section', 'updated_by', 'updated_at')
    list_filter = ('updated_at', 'updated_by')
    search_fields = ('hero_section__badge_en', 'updated_by__email')
    readonly_fields = ('hero_section', 'changes', 'updated_by', 'updated_at')
    
    def has_add_permission(self, request):
        # Revisions should only be created programmatically
        return False
    
    def has_change_permission(self, request, obj=None):
        # Revisions should not be editable
        return False


@admin.register(AboutUsSection)
class AboutUsSectionAdmin(admin.ModelAdmin):
    """
    Admin interface for About Us Section
    """
    list_display = ('id', 'title_en', 'is_active', 'updated_by', 'updated_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('title_en', 'title_ar', 'description_en', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Badge', {
            'fields': ('badge_en', 'badge_ar')
        }),
        ('Title & Subtitle', {
            'fields': (
                ('title_en', 'title_ar'),
                ('subtitle_en', 'subtitle_ar'),
            )
        }),
        ('Description', {
            'fields': ('description_en', 'description_ar')
        }),
        ('Image Overlay', {
            'fields': (
                ('image_overlay_badge_en', 'image_overlay_badge_ar'),
                ('image_overlay_text_en', 'image_overlay_text_ar'),
            )
        }),
        ('Main Image', {
            'fields': ('main_image_file',),
            'description': 'Upload an image file for the About Us section.'
        }),
        ('Image Alt Text', {
            'fields': ('image_alt_text_en', 'image_alt_text_ar'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('is_active', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(AboutUsSectionRevision)
class AboutUsSectionRevisionAdmin(admin.ModelAdmin):
    """
    Admin interface for About Us Section Revision model
    """
    list_display = ('id', 'about_us_section', 'updated_by', 'updated_at')
    list_filter = ('updated_at', 'updated_by')
    search_fields = ('about_us_section__title_en', 'updated_by__email')
    readonly_fields = ('updated_at',)
    ordering = ('-updated_at',)
    
    fieldsets = (
        (None, {
            'fields': ('about_us_section', 'changes', 'updated_by')
        }),
        ('Metadata', {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )


@admin.register(FeaturedProjectsSection)
class FeaturedProjectsSectionAdmin(admin.ModelAdmin):
    """
    Admin interface for Featured Projects Section model
    """
    list_display = ('id', 'title_en', 'title_ar', 'is_active', 'updated_by', 'updated_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('title_en', 'title_ar', 'description_en', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-updated_at',)
    
    fieldsets = (
        ('Title', {
            'fields': ('title_en', 'title_ar')
        }),
        ('Subtitle', {
            'fields': ('subtitle_en', 'subtitle_ar')
        }),
        ('Description', {
            'fields': ('description_en', 'description_ar')
        }),
        ('Button', {
            'fields': ('button_text_en', 'button_text_ar', 'button_link')
        }),
        ('Metadata', {
            'fields': ('is_active', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(FeaturedProjectsSectionRevision)
class FeaturedProjectsSectionRevisionAdmin(admin.ModelAdmin):
    """
    Admin interface for Featured Projects Section Revision model
    """
    list_display = ('id', 'featured_projects_section', 'updated_by', 'updated_at')
    list_filter = ('updated_at', 'updated_by')
    search_fields = ('featured_projects_section__title_en', 'updated_by__email')
    readonly_fields = ('updated_at',)
    ordering = ('-updated_at',)
    
    fieldsets = (
        (None, {
            'fields': ('featured_projects_section', 'changes', 'updated_by')
        }),
        ('Metadata', {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )


@admin.register(AchievementsSection)
class AchievementsSectionAdmin(admin.ModelAdmin):
    """
    Admin interface for Achievements Section model
    """
    list_display = ('id', 'title_en', 'title_ar', 'is_active', 'updated_by', 'updated_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('title_en', 'title_ar', 'supporting_text_en', 'supporting_text_ar')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-updated_at',)

    fieldsets = (
        ('Badge', {
            'fields': ('badge_en', 'badge_ar')
        }),
        ('Title', {
            'fields': ('title_en', 'title_ar')
        }),
        ('Supporting Text', {
            'fields': ('supporting_text_en', 'supporting_text_ar')
        }),
        ('Metadata', {
            'fields': ('is_active', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(AchievementsSectionRevision)
class AchievementsSectionRevisionAdmin(admin.ModelAdmin):
    """
    Admin interface for Achievements Section Revision model
    """
    list_display = ('id', 'achievements_section', 'updated_by', 'updated_at')
    list_filter = ('updated_at', 'updated_by')
    search_fields = ('achievements_section__title_en', 'updated_by__email')
    readonly_fields = ('updated_at',)
    ordering = ('-updated_at',)

    fieldsets = (
        (None, {
            'fields': ('achievements_section', 'changes', 'updated_by')
        }),
        ('Metadata', {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )


@admin.register(AchievementsItem)
class AchievementsItemAdmin(admin.ModelAdmin):
    """
    Admin interface for Achievements Item model
    """
    list_display = ('id', 'icon_name', 'achievement_value', 'title_en', 'is_active', 'updated_by', 'updated_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('icon_name', 'title_en', 'title_ar', 'achievement_value')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-updated_at',)

    fieldsets = (
        ('Icon Name', {
            'fields': ('icon_name',)
        }),
        ('Achievement Value', {
            'fields': ('achievement_value',)
        }),
        ('Title', {
            'fields': ('title_en', 'title_ar')
        }),
        ('Metadata', {
            'fields': ('is_active', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(AchievementsItemRevision)
class AchievementsItemRevisionAdmin(admin.ModelAdmin):
    """
    Admin interface for Achievements Item Revision model
    """
    list_display = ('id', 'achievements_item', 'updated_by', 'updated_at')
    list_filter = ('updated_at', 'updated_by')
    search_fields = ('achievements_item__title_en', 'updated_by__email')
    readonly_fields = ('updated_at',)
    ordering = ('-updated_at',)

    fieldsets = (
        (None, {
            'fields': ('achievements_item', 'changes', 'updated_by')
        }),
        ('Metadata', {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )


@admin.register(LatestArticlesSection)
class LatestArticlesSectionAdmin(admin.ModelAdmin):
    """
    Admin interface for Latest Articles Section model
    """
    list_display = ('id', 'title_en', 'title_ar', 'is_active', 'updated_by', 'updated_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('title_en', 'title_ar', 'description_en', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-updated_at',)

    fieldsets = (
        ('Badge', {
            'fields': ('badge_en', 'badge_ar')
        }),
        ('Title', {
            'fields': ('title_en', 'title_ar')
        }),
        ('Description', {
            'fields': ('description_en', 'description_ar')
        }),
        ('Button', {
            'fields': ('button_text_en', 'button_text_ar', 'button_link')
        }),
        ('Metadata', {
            'fields': ('is_active', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(LatestArticlesSectionRevision)
class LatestArticlesSectionRevisionAdmin(admin.ModelAdmin):
    """
    Admin interface for Latest Articles Section Revision model
    """
    list_display = ('id', 'latest_articles_section', 'updated_by', 'updated_at')
    list_filter = ('updated_at', 'updated_by')
    search_fields = ('latest_articles_section__title_en', 'updated_by__email')
    readonly_fields = ('updated_at',)
    ordering = ('-updated_at',)

    fieldsets = (
        (None, {
            'fields': ('latest_articles_section', 'changes', 'updated_by')
        }),
        ('Metadata', {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )


@admin.register(TestimonialsSection)
class TestimonialsSectionAdmin(admin.ModelAdmin):
    """
    Admin interface for Testimonials Section model
    """
    list_display = ('id', 'title_en', 'title_ar', 'is_active', 'updated_by', 'updated_at')
    list_filter = ('is_active', 'updated_at', 'created_at')
    search_fields = ('title_en', 'title_ar', 'description_en', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-updated_at',)

    fieldsets = (
        ('Badge', {
            'fields': ('badge_en', 'badge_ar')
        }),
        ('Title', {
            'fields': ('title_en', 'title_ar')
        }),
        ('Description', {
            'fields': ('description_en', 'description_ar')
        }),
        ('Metadata', {
            'fields': ('is_active', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TestimonialsSectionRevision)
class TestimonialsSectionRevisionAdmin(admin.ModelAdmin):
    """
    Admin interface for Testimonials Section Revision model
    """
    list_display = ('id', 'testimonials_section', 'updated_by', 'updated_at')
    list_filter = ('updated_at', 'updated_by')
    search_fields = ('testimonials_section__title_en', 'updated_by__email')
    readonly_fields = ('updated_at',)
    ordering = ('-updated_at',)

    fieldsets = (
        (None, {
            'fields': ('testimonials_section', 'changes', 'updated_by')
        }),
        ('Metadata', {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )


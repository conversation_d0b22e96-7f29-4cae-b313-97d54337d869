from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import URLValidator
from django.core.exceptions import ValidationError
import json
import os
from django.conf import settings

User = get_user_model()


class HeroSection(models.Model):
    """
    Model for home page hero section content with bilingual support
    """
    # Badge content
    badge_en = models.CharField(max_length=200, verbose_name="Badge (English)")
    badge_ar = models.Char<PERSON>ield(max_length=200, verbose_name="<PERSON>ge (Arabic)")
    
    # Title content (3 lines)
    title_line1_en = models.CharField(max_length=100, verbose_name="Title Line 1 (English)")
    title_line1_ar = models.Char<PERSON>ield(max_length=100, verbose_name="Title Line 1 (Arabic)")
    title_line2_en = models.CharField(max_length=100, verbose_name="Title Line 2 (English)")
    title_line2_ar = models.Char<PERSON><PERSON>(max_length=100, verbose_name="Title Line 2 (Arabic)")
    title_line3_en = models.Char<PERSON>ield(max_length=100, verbose_name="Title Line 3 (English)")
    title_line3_ar = models.Char<PERSON>ield(max_length=100, verbose_name="Title Line 3 (Arabic)")
    
    # Description content
    description_en = models.TextField(max_length=500, verbose_name="Description (English)")
    description_ar = models.TextField(max_length=500, verbose_name="Description (Arabic)")
    
    # Primary button
    primary_button_text_en = models.CharField(max_length=50, verbose_name="Primary Button Text (English)")
    primary_button_text_ar = models.CharField(max_length=50, verbose_name="Primary Button Text (Arabic)")
    primary_button_link = models.CharField(max_length=200, verbose_name="Primary Button Link")
    
    # Secondary button
    secondary_button_text_en = models.CharField(max_length=50, verbose_name="Secondary Button Text (English)")
    secondary_button_text_ar = models.CharField(max_length=50, verbose_name="Secondary Button Text (Arabic)")
    secondary_button_link = models.CharField(max_length=200, verbose_name="Secondary Button Link")
    
    # Bottom text
    bottom_text_en = models.CharField(max_length=100, verbose_name="Bottom Text (English)")
    bottom_text_ar = models.CharField(max_length=100, verbose_name="Bottom Text (Arabic)")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='hero_updates')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'homepage_hero_section'
        verbose_name = 'Hero Section'
        verbose_name_plural = 'Hero Sections'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Hero Section - {self.badge_en[:30]}..."
    
    def clean(self):
        """Validate URLs and content"""
        super().clean()
        
        # Validate button links
        url_validator = URLValidator()
        
        # Allow relative URLs or full URLs
        if self.primary_button_link and not self.primary_button_link.startswith('/'):
            try:
                url_validator(self.primary_button_link)
            except ValidationError:
                raise ValidationError({'primary_button_link': 'Invalid URL format'})
        
        if self.secondary_button_link and not self.secondary_button_link.startswith('/'):
            try:
                url_validator(self.secondary_button_link)
            except ValidationError:
                raise ValidationError({'secondary_button_link': 'Invalid URL format'})
    
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
    
    @property
    def structured_data(self):
        """Return data in the API response format"""
        return {
            'id': self.id,
            'badge': {
                'en': self.badge_en,
                'ar': self.badge_ar
            },
            'title': {
                'line1': {
                    'en': self.title_line1_en,
                    'ar': self.title_line1_ar
                },
                'line2': {
                    'en': self.title_line2_en,
                    'ar': self.title_line2_ar
                },
                'line3': {
                    'en': self.title_line3_en,
                    'ar': self.title_line3_ar
                }
            },
            'description': {
                'en': self.description_en,
                'ar': self.description_ar
            },
            'buttons': {
                'primary': {
                    'text': {
                        'en': self.primary_button_text_en,
                        'ar': self.primary_button_text_ar
                    },
                    'link': self.primary_button_link
                },
                'secondary': {
                    'text': {
                        'en': self.secondary_button_text_en,
                        'ar': self.secondary_button_text_ar
                    },
                    'link': self.secondary_button_link
                }
            },
            'bottomText': {
                'en': self.bottom_text_en,
                'ar': self.bottom_text_ar
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'updated_by': {
                'id': self.updated_by.id,
                'email': self.updated_by.email,
                'first_name': self.updated_by.first_name,
                'last_name': self.updated_by.last_name
            } if self.updated_by else None
        }


class HeroSectionRevision(models.Model):
    """
    Model to track revision history of hero section changes
    """
    hero_section = models.ForeignKey(HeroSection, on_delete=models.CASCADE, related_name='revisions')
    changes = models.JSONField(verbose_name="Changes Made")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'homepage_hero_revisions'
        verbose_name = 'Hero Section Revision'
        verbose_name_plural = 'Hero Section Revisions'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Revision {self.id} - {self.updated_at.strftime('%Y-%m-%d %H:%M')}"


class AboutUsSection(models.Model):
    """
    Model for home page About Us section content with bilingual support
    """
    # Badge content
    badge_en = models.CharField(max_length=100, verbose_name="Badge (English)")
    badge_ar = models.CharField(max_length=100, verbose_name="Badge (Arabic)")
    
    # Title content
    title_en = models.CharField(max_length=50, verbose_name="Title (English)")
    title_ar = models.CharField(max_length=50, verbose_name="Title (Arabic)")
    
    # Subtitle content
    subtitle_en = models.CharField(max_length=50, verbose_name="Subtitle (English)")
    subtitle_ar = models.CharField(max_length=50, verbose_name="Subtitle (Arabic)")
    
    # Description content
    description_en = models.TextField(max_length=1000, verbose_name="Description (English)")
    description_ar = models.TextField(max_length=1000, verbose_name="Description (Arabic)")
    
    # Image overlay content
    image_overlay_badge_en = models.CharField(max_length=50, verbose_name="Image Overlay Badge (English)")
    image_overlay_badge_ar = models.CharField(max_length=50, verbose_name="Image Overlay Badge (Arabic)")
    image_overlay_text_en = models.CharField(max_length=100, verbose_name="Image Overlay Text (English)")
    image_overlay_text_ar = models.CharField(max_length=100, verbose_name="Image Overlay Text (Arabic)")
    
    # Main image - only uploaded file (mandatory)
    main_image_file = models.ImageField(upload_to='uploads/about-us/', verbose_name="Main Image File")
    
    # Image metadata (for uploaded files)
    image_alt_text_en = models.CharField(max_length=255, blank=True, verbose_name="Image Alt Text (English)")
    image_alt_text_ar = models.CharField(max_length=255, blank=True, verbose_name="Image Alt Text (Arabic)")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='about_us_updates')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'homepage_about_us_section'
        verbose_name = 'About Us Section'
        verbose_name_plural = 'About Us Sections'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"About Us Section - {self.title_en}"
    
    @property
    def main_image(self):
        """Return the main image URL from file upload"""
        if self.main_image_file:
            return self.main_image_file.url
        else:
            return "/images/home/<USER>"  # Default fallback
    
    def clean(self):
        """Validate content and image fields"""
        super().clean()
        
        # Validate required fields
        if not self.badge_en or len(self.badge_en.strip()) < 3:
            raise ValidationError({'badge_en': 'Badge (English) must be at least 3 characters long'})
        
        if not self.badge_ar or len(self.badge_ar.strip()) < 3:
            raise ValidationError({'badge_ar': 'Badge (Arabic) must be at least 3 characters long'})
        
        if not self.title_en or len(self.title_en.strip()) < 2:
            raise ValidationError({'title_en': 'Title (English) must be at least 2 characters long'})
        
        if not self.title_ar or len(self.title_ar.strip()) < 2:
            raise ValidationError({'title_ar': 'Title (Arabic) must be at least 2 characters long'})
        
        if not self.description_en or len(self.description_en.strip()) < 10:
            raise ValidationError({'description_en': 'Description (English) must be at least 10 characters long'})
        
        if not self.description_ar or len(self.description_ar.strip()) < 10:
            raise ValidationError({'description_ar': 'Description (Arabic) must be at least 10 characters long'})
        
        # Validate that image file is provided
        if not self.main_image_file:
            raise ValidationError({'main_image_file': 'Main image file is required'})
    
    def save(self, *args, **kwargs):
        # Delete old file if replacing with new file
        if self.pk:
            try:
                old_instance = AboutUsSection.objects.get(pk=self.pk)
                if old_instance.main_image_file and self.main_image_file and old_instance.main_image_file != self.main_image_file:
                    # Replacing with new file, delete old file
                    if os.path.isfile(old_instance.main_image_file.path):
                        os.remove(old_instance.main_image_file.path)
            except (AboutUsSection.DoesNotExist, ValueError, OSError):
                pass  # Ignore errors when deleting old files
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        # Delete associated file when deleting the model instance
        if self.main_image_file:
            try:
                if os.path.isfile(self.main_image_file.path):
                    os.remove(self.main_image_file.path)
            except (ValueError, OSError):
                pass  # Ignore errors when deleting files
        super().delete(*args, **kwargs)
    
    @property
    def structured_data(self):
        """Return data in the API response format"""
        return {
            'id': self.id,
            'badge': {
                'en': self.badge_en,
                'ar': self.badge_ar
            },
            'title': {
                'en': self.title_en,
                'ar': self.title_ar
            },
            'subtitle': {
                'en': self.subtitle_en,
                'ar': self.subtitle_ar
            },
            'description': {
                'en': self.description_en,
                'ar': self.description_ar
            },
            'imageOverlay': {
                'badge': {
                    'en': self.image_overlay_badge_en,
                    'ar': self.image_overlay_badge_ar
                },
                'text': {
                    'en': self.image_overlay_text_en,
                    'ar': self.image_overlay_text_ar
                }
            },
            'mainImage': self.main_image,
            'mainImageFile': self.main_image_file.url if self.main_image_file else None,
            'imageAltText': {
                'en': self.image_alt_text_en,
                'ar': self.image_alt_text_ar
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'updated_by': {
                'id': self.updated_by.id,
                'email': self.updated_by.email,
                'first_name': self.updated_by.first_name,
                'last_name': self.updated_by.last_name
            } if self.updated_by else None
        }


class AboutUsSectionRevision(models.Model):
    """
    Model to track revision history of About Us section changes
    """
    about_us_section = models.ForeignKey(AboutUsSection, on_delete=models.CASCADE, related_name='revisions')
    changes = models.JSONField(verbose_name="Changes Made")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'homepage_about_us_revisions'
        verbose_name = 'About Us Section Revision'
        verbose_name_plural = 'About Us Section Revisions'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"About Us Revision {self.id} - {self.updated_at.strftime('%Y-%m-%d %H:%M')}"


class FeaturedProjectsSection(models.Model):
    """
    Model for home page Featured Projects section content with bilingual support
    """
    # Title content
    title_en = models.CharField(max_length=100, verbose_name="Title (English)")
    title_ar = models.CharField(max_length=100, verbose_name="Title (Arabic)")

    # Description content
    description_en = models.TextField(max_length=500, verbose_name="Description (English)")
    description_ar = models.TextField(max_length=500, verbose_name="Description (Arabic)")
    
    # Button content
    button_text_en = models.CharField(max_length=50, verbose_name="Button Text (English)")
    button_text_ar = models.CharField(max_length=50, verbose_name="Button Text (Arabic)")
    button_link = models.CharField(max_length=200, verbose_name="Button Link")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='featured_projects_updates')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'homepage_featured_projects_section'
        verbose_name = 'Featured Projects Section'
        verbose_name_plural = 'Featured Projects Sections'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Featured Projects Section - {self.title_en[:30]}..."
    
    def clean(self):
        """Validate URLs and content"""
        super().clean()
        
        # Validate button link
        url_validator = URLValidator()
        
        # Allow relative URLs or full URLs
        if self.button_link and not self.button_link.startswith('/'):
            try:
                url_validator(self.button_link)
            except ValidationError:
                raise ValidationError({'button_link': 'Invalid URL format'})
    
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
    
    @property
    def structured_data(self):
        """Return data in the API response format"""
        return {
            'id': self.id,
            'title': {
                'en': self.title_en,
                'ar': self.title_ar
            },
            'description': {
                'en': self.description_en,
                'ar': self.description_ar
            },
            'button': {
                'text': {
                    'en': self.button_text_en,
                    'ar': self.button_text_ar
                },
                'link': self.button_link
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'updated_by': {
                'id': self.updated_by.id,
                'email': self.updated_by.email,
                'first_name': self.updated_by.first_name,
                'last_name': self.updated_by.last_name
            } if self.updated_by else None
        }


class FeaturedProjectsSectionRevision(models.Model):
    """
    Model to track revision history of Featured Projects section changes
    """
    featured_projects_section = models.ForeignKey(FeaturedProjectsSection, on_delete=models.CASCADE, related_name='revisions')
    changes = models.JSONField(verbose_name="Changes Made")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'homepage_featured_projects_revisions'
        verbose_name = 'Featured Projects Section Revision'
        verbose_name_plural = 'Featured Projects Section Revisions'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Featured Projects Revision {self.id} - {self.updated_at.strftime('%Y-%m-%d %H:%M')}"


class AchievementsSection(models.Model):
    """
    Model for home page Achievements section content with bilingual support
    """
    # Badge content
    badge_en = models.CharField(max_length=100, verbose_name="Badge (English)")
    badge_ar = models.CharField(max_length=100, verbose_name="Badge (Arabic)")

    # Title content
    title_en = models.CharField(max_length=100, verbose_name="Title (English)")
    title_ar = models.CharField(max_length=100, verbose_name="Title (Arabic)")

    # Supporting text (short description)
    supporting_text_en = models.TextField(max_length=500, verbose_name="Supporting Text (English)")
    supporting_text_ar = models.TextField(max_length=500, verbose_name="Supporting Text (Arabic)")

    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='achievements_updates')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'homepage_achievements_section'
        verbose_name = 'Achievements Section'
        verbose_name_plural = 'Achievements Sections'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Achievements Section - {self.title_en[:30]}..."
    

    
    @property
    def structured_data(self):
        """Return data in the API response format"""
        return {
            'id': self.id,
            'badge': {
                'en': self.badge_en,
                'ar': self.badge_ar
            },
            'title': {
                'en': self.title_en,
                'ar': self.title_ar
            },
            'supporting_text': {
                'en': self.supporting_text_en,
                'ar': self.supporting_text_ar
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'updated_by': {
                'id': self.updated_by.id,
                'email': self.updated_by.email,
                'first_name': self.updated_by.first_name,
                'last_name': self.updated_by.last_name
            } if self.updated_by else None
        }


class AchievementsSectionRevision(models.Model):
    """
    Model to track revision history of Achievements section changes
    """
    achievements_section = models.ForeignKey(AchievementsSection, on_delete=models.CASCADE, related_name='revisions')
    changes = models.JSONField(verbose_name="Changes Made")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'homepage_achievements_revisions'
        verbose_name = 'Achievements Section Revision'
        verbose_name_plural = 'Achievements Section Revisions'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Achievements Revision {self.id} - {self.updated_at.strftime('%Y-%m-%d %H:%M')}"


class AchievementsItem(models.Model):
    """
    Model for home page Achievements section items
    """
    # Icon name (not translatable - just the icon identifier)
    icon_name = models.CharField(max_length=100, verbose_name="Icon Name")

    # Achievement value (not translatable - numbers/symbols only)
    achievement_value = models.CharField(max_length=50, verbose_name="Achievement Value")

    # Title content (bilingual)
    title_en = models.CharField(max_length=100, verbose_name="Title (English)")
    title_ar = models.CharField(max_length=100, verbose_name="Title (Arabic)")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='achievements_items_updates')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'homepage_achievements_items'
        verbose_name = 'Achievements Item'
        verbose_name_plural = 'Achievements Items'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Achievements Item - {self.icon_name} - {self.achievement_value}"

    @property
    def structured_data(self):
        """Return data in the API response format"""
        return {
            'id': self.id,
            'icon_name': self.icon_name,
            'achievement_value': self.achievement_value,
            'title': {
                'en': self.title_en,
                'ar': self.title_ar
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'updated_by': {
                'id': self.updated_by.id,
                'email': self.updated_by.email,
                'first_name': self.updated_by.first_name,
                'last_name': self.updated_by.last_name
            } if self.updated_by else None
        }


class AchievementsItemRevision(models.Model):
    """
    Model to track revision history of Achievements section items changes
    """
    achievements_item = models.ForeignKey(AchievementsItem, on_delete=models.CASCADE, related_name='revisions')
    changes = models.JSONField(verbose_name="Changes Made")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'homepage_achievements_item_revisions'
        verbose_name = 'Achievements Item Revision'
        verbose_name_plural = 'Achievements Item Revisions'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Achievements Item Revision {self.id} - {self.updated_at.strftime('%Y-%m-%d %H:%M')}" 
    

class LatestArticlesSection(models.Model):
    """
    Model for home page Latest Articles section content with bilingual support
    """
    # Badge content
    badge_en = models.CharField(max_length=100, verbose_name="Badge (English)")
    badge_ar = models.CharField(max_length=100, verbose_name="Badge (Arabic)")

    # Title content
    title_en = models.CharField(max_length=100, verbose_name="Title (English)")
    title_ar = models.CharField(max_length=100, verbose_name="Title (Arabic)")

    # Description content
    description_en = models.TextField(max_length=500, verbose_name="Description (English)")
    description_ar = models.TextField(max_length=500, verbose_name="Description (Arabic)")

    # Button content
    button_text_en = models.CharField(max_length=50, verbose_name="Button Text (English)")
    button_text_ar = models.CharField(max_length=50, verbose_name="Button Text (Arabic)")
    button_link = models.CharField(max_length=200, verbose_name="Button Link")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='latest_articles_updates')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'homepage_latest_articles_section'
        verbose_name = 'Latest Articles Section'
        verbose_name_plural = 'Latest Articles Sections'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Latest Articles Section - {self.title_en[:30]}..."
    
    
    @property
    def structured_data(self):
        """Return data in the API response format"""
        return {
            'id': self.id,          
            'badge': {
                'en': self.badge_en,
                'ar': self.badge_ar
            },
            'title': {
                'en': self.title_en,
                'ar': self.title_ar
            },
            'description': {
                'en': self.description_en,
                'ar': self.description_ar
            },
            'button': {
                'text': {
                    'en': self.button_text_en,
                    'ar': self.button_text_ar
                },
                'link': self.button_link
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'updated_by': {
                'id': self.updated_by.id,
                'email': self.updated_by.email,
                'first_name': self.updated_by.first_name,
                'last_name': self.updated_by.last_name
            } if self.updated_by else None
        }

class LatestArticlesSectionRevision(models.Model):
    """
    Model to track revision history of Latest Articles section changes
    """
    latest_articles_section = models.ForeignKey(LatestArticlesSection, on_delete=models.CASCADE, related_name='revisions')
    changes = models.JSONField(verbose_name="Changes Made")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'homepage_latest_articles_revisions'
        verbose_name = 'Latest Articles Section Revision'
        verbose_name_plural = 'Latest Articles Section Revisions'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Latest Articles Revision {self.id} - {self.updated_at.strftime('%Y-%m-%d %H:%M')}"

class TestimonialsSection(models.Model):
    """
    Model for home page Testimonials section content with bilingual support
    """
    # Badge content
    badge_en = models.CharField(max_length=100, verbose_name="Badge (English)")
    badge_ar = models.CharField(max_length=100, verbose_name="Badge (Arabic)")

    # Title content
    title_en = models.CharField(max_length=100, verbose_name="Title (English)")
    title_ar = models.CharField(max_length=100, verbose_name="Title (Arabic)")

    # Description content
    description_en = models.TextField(max_length=500, verbose_name="Description (English)")
    description_ar = models.TextField(max_length=500, verbose_name="Description (Arabic)")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='testimonials_updates')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'homepage_testimonials_section'
        verbose_name = 'Testimonials Section'
        verbose_name_plural = 'Testimonials Sections'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Testimonials Section - {self.title_en[:30]}..."
    
    
    @property
    def structured_data(self):
        """Return data in the API response format"""
        return {
            'id': self.id,          
            'badge': {
                'en': self.badge_en,
                'ar': self.badge_ar
            },
            'title': {
                'en': self.title_en,
                'ar': self.title_ar
            },
            'description': {
                'en': self.description_en,
                'ar': self.description_ar
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'updated_by': {
                'id': self.updated_by.id,
                'email': self.updated_by.email,
                'first_name': self.updated_by.first_name,
                'last_name': self.updated_by.last_name
            } if self.updated_by else None
        }

class TestimonialsSectionRevision(models.Model):
    """
    Model to track revision history of Testimonials section changes
    """
    testimonials_section = models.ForeignKey(TestimonialsSection, on_delete=models.CASCADE, related_name='revisions')
    changes = models.JSONField(verbose_name="Changes Made")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'homepage_testimonials_revisions'
        verbose_name = 'Testimonials Section Revision'
        verbose_name_plural = 'Testimonials Section Revisions'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Testimonials Revision {self.id} - {self.updated_at.strftime('%Y-%m-%d %H:%M')}"

